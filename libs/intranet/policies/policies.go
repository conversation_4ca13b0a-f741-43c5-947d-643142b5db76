// Package policies provides policy evaluation and authorization functionality using Open Policy Agent (OPA).
// It includes AWS Cognito JWT validation, role-based access control (RBAC), and policy decision making
// for the SA Intranet application security framework.
package policies

import (
	"context"
	_ "embed"
	"errors"
	"fmt"

	"sa-intranet/core"

	"github.com/open-policy-agent/opa/v1/rego"
	"github.com/samber/do"
)

// awsCognitoRego contains the embedded Rego policy for AWS Cognito JWT validation
//
//go:embed aws-cognito.rego
var awsCognitoRego []byte

// Error definitions for policy evaluation operations
var (
	// ErrNoResults is returned when policy evaluation produces no results
	ErrNoResults = errors.New("query evaluation returned no results")
	// ErrUnexpectedResult is returned when policy evaluation result has unexpected type
	ErrUnexpectedResult = errors.New("unexpected result type")
	// ErrUnexpectedJWTHeader is returned when JWT header parsing fails
	ErrUnexpectedJWTHeader = errors.New("unexpected JWT header type")
	// ErrUnexpectedJWTPayload is returned when JWT payload parsing fails
	ErrUnexpectedJWTPayload = errors.New("unexpected JWT payload type")
	// ErrUnexpectedJWTSignature is returned when JWT signature parsing fails
	ErrUnexpectedJWTSignature = errors.New("unexpected JWT signature type")
	// ErrUnexpectedJWTData is returned when JWT data structure is invalid
	ErrUnexpectedJWTData = errors.New("unexpected JWT data type")
	// ErrInvalidJWTArrayLength is returned when JWT array doesn't have exactly 3 elements
	ErrInvalidJWTArrayLength = errors.New("JWT array must have exactly 3 elements (header, payload, signature)")
)

// JwtDataPayload represents the payload section of an AWS Cognito JWT token.
// It contains user identity information and authentication metadata.
type JwtDataPayload struct {
	// Email is the user's email address
	Email string `json:"email"`
	// EmailVerified indicates if the email has been verified
	EmailVerified string `json:"email_verified"`
	// FamilyName is the user's last name
	FamilyName string `json:"family_name"`
	// GivenName is the user's first name
	GivenName string `json:"given_name"`
	// ISS is the token issuer (AWS Cognito)
	ISS string `json:"iss"`
	// Sub is the unique user identifier
	Sub string `json:"sub"`
	// Username is the user's username
	Username string `json:"username"`
	// Exp is the token expiration timestamp
	Exp int `json:"exp"`
	// Identities contains identity provider information
	Identities string `json:"identities"`
}

// JwtDataHeader represents the header section of an AWS Cognito JWT token.
// It contains cryptographic and metadata information about the token.
type JwtDataHeader struct {
	// Alg is the signing algorithm used
	Alg string `json:"alg"`
	// Client is the client identifier
	Client string `json:"client"`
	// Kid is the key identifier for signature verification
	Kid string `json:"kid"`
	// Exp is the header expiration timestamp
	Exp int `json:"exp"`
	// Typ is the token type (typically "JWT")
	Typ string `json:"typ"`
	// ISS is the token issuer
	ISS string `json:"iss"`
	// Signer is the signing entity identifier
	Signer string `json:"signer"`
}

// mapToJwtDataHeader converts a generic map to a strongly-typed JwtDataHeader struct.
// It safely extracts and type-asserts each field from the map.
func mapToJwtDataHeader(header map[string]any) JwtDataHeader {
	var jwtHeader JwtDataHeader

	// Manual field assignment
	if alg, ok := header["alg"].(string); ok {
		jwtHeader.Alg = alg
	}

	if client, ok := header["client"].(string); ok {
		jwtHeader.Client = client
	}

	if kid, ok := header["kid"].(string); ok {
		jwtHeader.Kid = kid
	}

	if exp, ok := header["exp"].(int); ok {
		jwtHeader.Exp = exp
	}

	if typ, ok := header["typ"].(string); ok {
		jwtHeader.Typ = typ
	}

	if iss, ok := header["iss"].(string); ok {
		jwtHeader.ISS = iss
	}

	if signer, ok := header["signer"].(string); ok {
		jwtHeader.Signer = signer
	}

	return jwtHeader
}

// mapToJwtDataPayload converts a generic map to a strongly-typed JwtDataPayload struct.
// It safely extracts and type-asserts each field from the map.
func mapToJwtDataPayload(payload map[string]any) JwtDataPayload {
	var jwtPayload JwtDataPayload

	// Manual field assignment
	if email, ok := payload["email"].(string); ok {
		jwtPayload.Email = email
	}

	if emailVerified, ok := payload["email_verified"].(string); ok {
		jwtPayload.EmailVerified = emailVerified
	}

	if familyName, ok := payload["family_name"].(string); ok {
		jwtPayload.FamilyName = familyName
	}

	if givenName, ok := payload["given_name"].(string); ok {
		jwtPayload.GivenName = givenName
	}

	if iss, ok := payload["iss"].(string); ok {
		jwtPayload.ISS = iss
	}

	if sub, ok := payload["sub"].(string); ok {
		jwtPayload.Sub = sub
	}

	if username, ok := payload["username"].(string); ok {
		jwtPayload.Username = username
	}

	if exp, ok := payload["exp"].(int); ok {
		jwtPayload.Exp = exp
	}

	if identities, ok := payload["identities"].(string); ok {
		jwtPayload.Identities = identities
	}

	return jwtPayload
}

type JwtData struct {
	Header    JwtDataHeader
	Payload   JwtDataPayload
	Signature string
}

func EvalCognitoPolicy(ctx context.Context, input any) (JwtData, error) {
	var jwtData JwtData

	query, err := prepareCognitoQuery(ctx)
	if err != nil {
		return jwtData, err
	}

	results, err := executePolicyQuery(ctx, query, input)
	if err != nil {
		return jwtData, err
	}

	allow, jwt, err := extractPolicyResults(results)
	if err != nil {
		return jwtData, err
	}

	if !allow {
		return jwtData, ErrAccessDenied
	}

	jwtData, err = parseJwtData(jwt)
	if err != nil {
		return jwtData, err
	}

	return jwtData, nil
}

func prepareCognitoQuery(ctx context.Context) (rego.PreparedEvalQuery, error) {
	r := rego.New(
		rego.Query("x=data.awscognito.allow; jwt=data.awscognito.jwt"),
		rego.Module("aws-cognito.rego", string(awsCognitoRego)),
		rego.EnablePrintStatements(true),
	)

	query, err := r.PrepareForEval(ctx)
	if err != nil {
		return query, fmt.Errorf("error preparing query: %w", err)
	}

	return query, nil
}

func executePolicyQuery(ctx context.Context, query rego.PreparedEvalQuery, input any) (rego.ResultSet, error) {
	results, err := query.Eval(ctx, rego.EvalInput(input))
	if err != nil {
		return nil, fmt.Errorf("error evaluating query: %w", err)
	}

	if len(results) == 0 {
		return nil, ErrNoResults
	}

	return results, nil
}

func extractPolicyResults(results rego.ResultSet) (bool, []any, error) {
	allow, ok := results[0].Bindings["x"].(bool)
	if !ok {
		return false, nil, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedResult, results[0].Bindings["x"])
	}

	jwt, ok := results[0].Bindings["jwt"].([]any)
	if !ok {
		return false, nil, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTData, results[0].Bindings["jwt"])
	}

	return allow, jwt, nil
}

func parseJwtData(jwt []any) (JwtData, error) {
	var jwtData JwtData

	if len(jwt) != 3 {
		return jwtData, ErrInvalidJWTArrayLength
	}

	mapHeaderData, headerOk := jwt[0].(map[string]any)
	if !headerOk {
		return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTHeader, jwt[0])
	}

	jwtData.Header = mapToJwtDataHeader(mapHeaderData)

	mapPayloadData, payloadOk := jwt[1].(map[string]any)
	if !payloadOk {
		return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTPayload, jwt[1])
	}

	jwtData.Payload = mapToJwtDataPayload(mapPayloadData)

	signature, signatureOk := jwt[2].(string)
	if !signatureOk {
		return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTSignature, jwt[2])
	}

	jwtData.Signature = signature

	return jwtData, nil
}

func Register(i *do.Injector) error {
	do.Provide(i, NewRBACPolicy)
	return nil
}

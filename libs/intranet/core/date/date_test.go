package date

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDate_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name    string
		input   []byte
		want    time.Time
		wantErr bool
	}{
		{
			name:    "valid date",
			input:   []byte(`"2023-12-25"`),
			want:    time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC),
			wantErr: false,
		},
		{
			name:    "valid date with quotes",
			input:   []byte(`"2024-01-01"`),
			want:    time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			wantErr: false,
		},
		{
			name:    "invalid date format",
			input:   []byte(`"2023-13-25"`),
			want:    time.Time{},
			wantErr: true,
		},
		{
			name:    "invalid date string",
			input:   []byte(`"not-a-date"`),
			want:    time.Time{},
			wantErr: true,
		},
		{
			name:    "empty string",
			input:   []byte(`""`),
			want:    time.Time{},
			wantErr: true,
		},
		{
			name:    "wrong format",
			input:   []byte(`"25-12-2023"`),
			want:    time.Time{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var d Date
			err := d.UnmarshalJSON(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, d.Time)
			}
		})
	}
}

func TestDate_MarshalJSON(t *testing.T) {
	tests := []struct {
		name string
		date Date
		want string
	}{
		{
			name: "valid date",
			date: Date{Time: time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC)},
			want: `"2023-12-25"`,
		},
		{
			name: "new year date",
			date: Date{Time: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)},
			want: `"2024-01-01"`,
		},
		{
			name: "leap year date",
			date: Date{Time: time.Date(2024, 2, 29, 0, 0, 0, 0, time.UTC)},
			want: `"2024-02-29"`,
		},
		{
			name: "zero date",
			date: Date{Time: time.Time{}},
			want: `"0001-01-01"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.date.MarshalJSON()

			assert.NoError(t, err)
			assert.Equal(t, tt.want, string(result))
		})
	}
}

func TestDate_JSONRoundTrip(t *testing.T) {
	tests := []struct {
		name string
		date Date
	}{
		{
			name: "round trip test",
			date: Date{Time: time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC)},
		},
		{
			name: "another date",
			date: Date{Time: time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC)},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Marshal to JSON
			jsonData, err := json.Marshal(tt.date)
			require.NoError(t, err)

			// Unmarshal back to Date
			var unmarshaled Date
			err = json.Unmarshal(jsonData, &unmarshaled)
			require.NoError(t, err)

			// Compare the dates (ignoring time zone and sub-day precision)
			assert.Equal(t, tt.date.Year(), unmarshaled.Year())
			assert.Equal(t, tt.date.Month(), unmarshaled.Month())
			assert.Equal(t, tt.date.Day(), unmarshaled.Day())
		})
	}
}

func TestDate_Integration(t *testing.T) {
	t.Run("struct with date field", func(t *testing.T) {
		type TestStruct struct {
			Name string `json:"name"`
			Date Date   `json:"date"`
		}

		original := TestStruct{
			Name: "Test Event",
			Date: Date{Time: time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC)},
		}

		// Marshal to JSON
		jsonData, err := json.Marshal(original)
		require.NoError(t, err)

		expectedJSON := `{"name":"Test Event","date":"2023-12-25"}`
		assert.JSONEq(t, expectedJSON, string(jsonData))

		// Unmarshal back
		var unmarshaled TestStruct
		err = json.Unmarshal(jsonData, &unmarshaled)
		require.NoError(t, err)

		assert.Equal(t, original.Name, unmarshaled.Name)
		assert.Equal(t, original.Date.Year(), unmarshaled.Date.Year())
		assert.Equal(t, original.Date.Month(), unmarshaled.Date.Month())
		assert.Equal(t, original.Date.Day(), unmarshaled.Date.Day())
	})
}

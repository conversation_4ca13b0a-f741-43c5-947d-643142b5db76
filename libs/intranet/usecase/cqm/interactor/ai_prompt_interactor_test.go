package interactor_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// generateIssueKeys creates a slice of dummy issue keys for testing
func generateIssueKeys(count int) []string {
	keys := make([]string, count)
	for i := 0; i < count; i++ {
		keys[i] = fmt.Sprintf("TEST-ISSUE-%d", i+1)
	}
	return keys
}

func TestAIPromptInteractorGeneratePrompt(t *testing.T) {
	tests := []struct {
		name    string
		request interactor.GenerateAIPromptRequest
		setup   func(t *testing.T) (string, *httptest.Server) // returns projectID and mock server
		wantErr bool
		verify  func(t *testing.T, response interactor.AIPromptResponse, err error)
	}{
		{
			name: "successful prompt generation with single issue",
			request: interactor.GenerateAIPromptRequest{
				IssueKeys: []string{"AZUqtlaa7J7LX6RjQIpc"},
			},
			setup: func(t *testing.T) (string, *httptest.Server) {
				projectID, mockServer := setupTestProjectAndMockServer(t, []string{"AZUqtlaa7J7LX6RjQIpc"})
				return projectID, mockServer
			},
			wantErr: false,
			verify: func(t *testing.T, response interactor.AIPromptResponse, err error) {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
					return
				}
				if response.IssueCount != 1 {
					t.Errorf("Expected 1 issue, got %d", response.IssueCount)
				}
				if response.Prompt == "" {
					t.Error("Expected non-empty prompt")
				}
				if !strings.Contains(response.Prompt, "SonarQube Issues AI Fix Prompt") {
					t.Error("Expected prompt to contain header")
				}
				if !strings.Contains(response.Prompt, "AZUqtlaa7J7LX6RjQIpc") {
					t.Error("Expected prompt to contain issue key")
				}
				if response.ProjectInfo.Name == "" {
					t.Error("Expected project info to be populated")
				}
				if len(response.Issues) != 1 {
					t.Errorf("Expected 1 issue detail, got %d", len(response.Issues))
				}
			},
		},
		{
			name: "successful prompt generation with multiple issues",
			request: interactor.GenerateAIPromptRequest{
				IssueKeys: []string{"AZUqtlaa7J7LX6RjQIpc", "BZUqtlaa7J7LX6RjQIpd"},
			},
			setup: func(t *testing.T) (string, *httptest.Server) {
				projectID, mockServer := setupTestProjectAndMockServer(t, []string{"AZUqtlaa7J7LX6RjQIpc", "BZUqtlaa7J7LX6RjQIpd"})
				return projectID, mockServer
			},
			wantErr: false,
			verify: func(t *testing.T, response interactor.AIPromptResponse, err error) {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
					return
				}
				if response.IssueCount != 2 {
					t.Errorf("Expected 2 issues, got %d", response.IssueCount)
				}
				if len(response.Issues) != 2 {
					t.Errorf("Expected 2 issue details, got %d", len(response.Issues))
				}
				if !strings.Contains(response.Prompt, "Priority order for fixing") {
					t.Error("Expected prompt to contain priority order instruction for multiple issues")
				}
			},
		},
		{
			name: "validation error - empty issue keys",
			request: interactor.GenerateAIPromptRequest{
				IssueKeys: []string{},
			},
			setup: func(t *testing.T) (string, *httptest.Server) {
				projectID, mockServer := setupTestProjectAndMockServer(t, []string{})
				return projectID, mockServer
			},
			wantErr: true,
			verify: func(t *testing.T, response interactor.AIPromptResponse, err error) {
				if err == nil {
					t.Error("Expected validation error for empty issue keys")
				}
			},
		},
		{
			name: "validation error - too many issues",
			request: interactor.GenerateAIPromptRequest{
				IssueKeys: generateIssueKeys(51), // More than MaxIssuesPerRequest
			},
			setup: func(t *testing.T) (string, *httptest.Server) {
				// Create request with too many issue keys
				projectID, mockServer := setupTestProjectAndMockServer(t, []string{})
				return projectID, mockServer
			},
			wantErr: true,
			verify: func(t *testing.T, response interactor.AIPromptResponse, err error) {
				if !strings.Contains(err.Error(), "too many issues requested") {
					t.Errorf("Expected error about too many issues, got %v", err)
				}
			},
		},
		{
			name: "project not found error",
			request: interactor.GenerateAIPromptRequest{
				ProjectID: "00000000-0000-0000-0000-000000000000",
				IssueKeys: []string{"AZUqtlaa7J7LX6RjQIpc"},
			},
			setup: func(t *testing.T) (string, *httptest.Server) {
				// Return non-existent project ID
				_, mockServer := setupTestProjectAndMockServer(t, []string{"AZUqtlaa7J7LX6RjQIpc"})
				return "00000000-0000-0000-0000-000000000000", mockServer
			},
			wantErr: true,
			verify: func(t *testing.T, response interactor.AIPromptResponse, err error) {
				if !strings.Contains(err.Error(), "project not found") {
					t.Errorf("Expected project not found error, got %v", err)
				}
			},
		},
		{
			name: "invalid project ID format",
			request: interactor.GenerateAIPromptRequest{
				ProjectID: "invalid-uuid",
				IssueKeys: []string{"AZUqtlaa7J7LX6RjQIpc"},
			},
			setup: func(t *testing.T) (string, *httptest.Server) {
				_, mockServer := setupTestProjectAndMockServer(t, []string{"AZUqtlaa7J7LX6RjQIpc"})
				return "invalid-uuid", mockServer
			},
			wantErr: true,
			verify: func(t *testing.T, response interactor.AIPromptResponse, err error) {
				if !strings.Contains(err.Error(), "validation error") || !strings.Contains(err.Error(), "uuid") {
					t.Errorf("Expected validation error for invalid UUID format, got %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test environment
			projectID, mockServer := tt.setup(t)
			defer mockServer.Close()

			// Override SonarQube client configuration
			inj := injector.Clone()
			do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
				return sonarqube.ClientConfig{
					URL:   mockServer.URL,
					Token: "test-token",
				}, nil
			})

			// Get the AI prompt interactor
			aiPromptInteractor := do.MustInvoke[*interactor.AIPromptInteractor](inj)

			// Set project ID in request
			tt.request.ProjectID = projectID

			// Execute the test
			response, err := aiPromptInteractor.GeneratePrompt(context.Background(), tt.request)

			// Verify results
			if (err != nil) != tt.wantErr {
				t.Errorf("GeneratePrompt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			tt.verify(t, response, err)
		})
	}
}

func TestAIPromptInteractorValidation(t *testing.T) {
	inj := injector.Clone()
	aiPromptInteractor := do.MustInvoke[*interactor.AIPromptInteractor](inj)

	tests := []struct {
		name    string
		request interactor.GenerateAIPromptRequest
		wantErr bool
	}{
		{
			name: "valid request format (will fail on project lookup)",
			request: interactor.GenerateAIPromptRequest{
				ProjectID: uuid.New().String(),
				IssueKeys: []string{"valid-issue-key"},
			},
			wantErr: true, // Will fail because project doesn't exist, but validation passes
		},
		{
			name: "missing project ID",
			request: interactor.GenerateAIPromptRequest{
				IssueKeys: []string{"valid-issue-key"},
			},
			wantErr: true,
		},
		{
			name: "invalid project ID format",
			request: interactor.GenerateAIPromptRequest{
				ProjectID: "not-a-uuid",
				IssueKeys: []string{"valid-issue-key"},
			},
			wantErr: true,
		},
		{
			name: "empty issue keys",
			request: interactor.GenerateAIPromptRequest{
				ProjectID: uuid.New().String(),
				IssueKeys: []string{},
			},
			wantErr: true,
		},
		{
			name: "issue key with empty string",
			request: interactor.GenerateAIPromptRequest{
				ProjectID: uuid.New().String(),
				IssueKeys: []string{"valid-key", ""},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := aiPromptInteractor.GeneratePrompt(context.Background(), tt.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GeneratePrompt() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// setupTestProjectAndMockServer creates a test project and mock SonarQube server
func setupTestProjectAndMockServer(t *testing.T, issueKeys []string) (string, *httptest.Server) {
	t.Helper()

	// Generate unique identifiers for this test
	testID := uuid.New().String()[:8]

	// Create test data
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](injector)
	companyClient := &model.CompanyClient{
		Name: "Test Company for AI Prompt " + testID,
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](injector)
	jiraProject := &model.JiraProject{
		ProjectKey:      "AITEST" + testID,
		Name:            "AI Test Jira Project " + testID,
		JiraURL:         "https://aitest" + testID + ".atlassian.net",
		Username:        "aitest" + testID + "@example.com",
		Active:          true,
		Token:           "encrypted-token-" + testID,
		CompanyClientID: savedClient.ID,
	}
	savedJiraProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](injector)
	sonarProject := &model.SonarqubeProject{
		ProjectKey:    "ai-test-sonar-project-" + testID,
		ProjectName:   "AI Test SonarQube Project " + testID,
		Branch:        "main",
		JiraProjectID: savedJiraProject.ID,
		Active:        true,
	}
	savedSonarProject, err := sonarProjectRepo.Save(context.Background(), sonarProject, true)
	if err != nil {
		t.Fatalf("Failed to create test SonarQube project: %v", err)
	}

	// Create mock SonarQube server
	mockServer := createAIPromptMockSonarServer(issueKeys)

	return savedSonarProject.ID.String(), mockServer
}

// createAIPromptMockSonarServer creates a mock SonarQube server for AI prompt tests
func createAIPromptMockSonarServer(issueKeys []string) *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/api/issues/search":
			handleMockIssuesSearch(w, r, issueKeys)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

// handleMockIssuesSearch handles the mock SonarQube issues search endpoint
func handleMockIssuesSearch(w http.ResponseWriter, r *http.Request, issueKeys []string) {
	// Create mock issues based on the requested issue keys
	issues := make([]map[string]interface{}, 0, len(issueKeys))

	for i, key := range issueKeys {
		issue := map[string]interface{}{
			"key":          key,
			"rule":         "java:S1144",
			"severity":     "MAJOR",
			"component":    "com.example:test-project:src/main/java/com/example/TestClass.java",
			"project":      "com.example:test-project",
			"line":         42 + i,
			"status":       "OPEN",
			"message":      "Remove this unused private method.",
			"type":         "CODE_SMELL",
			"creationDate": "2023-01-01T10:00:00+0000",
			"updateDate":   "2023-01-01T10:00:00+0000",
			"tags":         []string{"unused", "dead-code"},
			"author":       "<EMAIL>",
			"effort":       "5min",
		}
		issues = append(issues, issue)
	}

	response := map[string]interface{}{
		"total":  len(issues),
		"p":      1,
		"ps":     len(issues),
		"issues": issues,
		"components": []map[string]interface{}{
			{
				"key":       "com.example:test-project:src/main/java/com/example/TestClass.java",
				"enabled":   true,
				"qualifier": "FIL",
				"name":      "TestClass.java",
				"longName":  "src/main/java/com/example/TestClass.java",
				"path":      "src/main/java/com/example/TestClass.java",
			},
		},
		"paging": map[string]interface{}{
			"pageIndex": 1,
			"pageSize":  len(issues),
			"total":     len(issues),
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

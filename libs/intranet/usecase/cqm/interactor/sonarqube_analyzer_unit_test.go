package interactor

import (
	"testing"

	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/stretchr/testify/assert"
)

// TestPopulateAnalysisDataUnit tests the populateAnalysisData method directly
func TestPopulateAnalysisDataUnit(t *testing.T) {
	tests := []struct {
		name                string
		measures            sonarqube.GetMeasuresResponse
		sonarIssuesResponse sonarqube.IssuesResponse
		expectedTotal       int
		expectedEffort      int
	}{
		{
			name: "successful data population",
			measures: sonarqube.GetMeasuresResponse{
				Component: struct {
					Key       string `json:"key"`
					Name      string `json:"name"`
					Qualifier string `json:"qualifier"`
					Measures  []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					} `json:"measures"`
				}{
					Key:       "test-project",
					Name:      "Test Project",
					Qualifier: "TRK",
				},
			},
			sonarIssuesResponse: sonarqube.IssuesResponse{
				Total:       10,
				EffortTotal: 120,
			},
			expectedTotal:  10,
			expectedEffort: 120,
		},
		{
			name: "zero values",
			measures: sonarqube.GetMeasuresResponse{
				Component: struct {
					Key       string `json:"key"`
					Name      string `json:"name"`
					Qualifier string `json:"qualifier"`
					Measures  []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					} `json:"measures"`
				}{
					Key:       "empty-project",
					Name:      "Empty Project",
					Qualifier: "TRK",
				},
			},
			sonarIssuesResponse: sonarqube.IssuesResponse{
				Total:       0,
				EffortTotal: 0,
			},
			expectedTotal:  0,
			expectedEffort: 0,
		},
		{
			name: "large values",
			measures: sonarqube.GetMeasuresResponse{
				Component: struct {
					Key       string `json:"key"`
					Name      string `json:"name"`
					Qualifier string `json:"qualifier"`
					Measures  []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					} `json:"measures"`
				}{
					Key:       "large-project",
					Name:      "Large Project",
					Qualifier: "TRK",
				},
			},
			sonarIssuesResponse: sonarqube.IssuesResponse{
				Total:       9999,
				EffortTotal: 50000,
			},
			expectedTotal:  9999,
			expectedEffort: 50000,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal analyzer instance for testing
			analyzer := &SonarAnalizerInteractor{}

			// Create a test analysis object
			analysis := &model.SonarqubeAnalysis{}

			// Call the method under test
			analyzer.populateAnalysisData(analysis, tt.measures, tt.sonarIssuesResponse)

			// Verify the results
			assert.NotNil(t, analysis.Measures)
			assert.Equal(t, tt.expectedTotal, analysis.IssuesTotal)
			assert.Equal(t, tt.expectedEffort, analysis.IssuesEffortTotal)

			// Verify measures data structure
			measuresData, ok := analysis.Measures["data"]
			assert.True(t, ok, "Measures should contain 'data' key")
			assert.Equal(t, tt.measures, measuresData, "Measures data should match input")

			// Verify component data is preserved
			measures, ok := measuresData.(sonarqube.GetMeasuresResponse)
			assert.True(t, ok, "Measures data should be of correct type")
			assert.Equal(t, tt.measures.Component.Key, measures.Component.Key)
			assert.Equal(t, tt.measures.Component.Name, measures.Component.Name)
			assert.Equal(t, tt.measures.Component.Qualifier, measures.Component.Qualifier)
		})
	}
}

// TestProcessAnalysisStructure tests the structure and flow of the refactored processAnalysis method
func TestProcessAnalysisStructure(t *testing.T) {
	t.Run("processAnalysis method structure", func(t *testing.T) {
		// This test verifies that the processAnalysis method has been properly refactored
		// and follows the expected structure with helper functions

		// Create a minimal analyzer instance
		analyzer := &SonarAnalizerInteractor{}

		// Verify that the helper methods exist and are accessible
		// (This is a compile-time check - if the methods don't exist, this won't compile)

		// Test that populateAnalysisData method exists and can be called
		analysis := &model.SonarqubeAnalysis{}
		measures := sonarqube.GetMeasuresResponse{}
		issues := sonarqube.IssuesResponse{}

		// This should not panic and should execute successfully
		analyzer.populateAnalysisData(analysis, measures, issues)

		// Verify basic functionality
		assert.NotNil(t, analysis.Measures)
		assert.Equal(t, 0, analysis.IssuesTotal)
		assert.Equal(t, 0, analysis.IssuesEffortTotal)
	})
}

// TestSonarQubeWebhookStructures tests the webhook data structures
func TestSonarQubeWebhookStructures(t *testing.T) {
	tests := []struct {
		name    string
		webhook SonarQubeWebhook
	}{
		{
			name: "complete webhook structure",
			webhook: SonarQubeWebhook{
				ServerURL:  "https://sonarqube.example.com",
				TaskID:     "task-123",
				Status:     "SUCCESS",
				AnalysedAt: "2023-01-01T00:00:00Z",
				Project: SonarQubeWebhookProject{
					Key:  "test-project",
					Name: "Test Project",
					URL:  "https://sonarqube.example.com/project",
				},
				Branch: SonarQubeWebhookBranch{
					Name:   "main",
					Type:   "BRANCH",
					IsMain: true,
					URL:    "https://sonarqube.example.com/branch",
				},
				QualityGate: SonarQubeWebhookQualityGate{
					Name:   "Sonar way",
					Status: "OK",
					Conditions: []SonarQubeWebhookCondition{
						{
							Metric:         "coverage",
							Operator:       "LT",
							Value:          "80.0",
							Status:         "OK",
							ErrorThreshold: "70.0",
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify webhook structure is properly populated
			assert.NotEmpty(t, tt.webhook.ServerURL)
			assert.NotEmpty(t, tt.webhook.TaskID)
			assert.NotEmpty(t, tt.webhook.Status)
			assert.NotEmpty(t, tt.webhook.Project.Key)
			assert.NotEmpty(t, tt.webhook.Branch.Name)
			assert.NotEmpty(t, tt.webhook.QualityGate.Name)
			assert.Greater(t, len(tt.webhook.QualityGate.Conditions), 0)
		})
	}
}

// TestFetchAnalysisDataConcurrentlyErrorHandling tests error scenarios in the concurrent data fetching
func TestFetchAnalysisDataConcurrentlyErrorHandling(t *testing.T) {
	tests := []struct {
		name        string
		description string
	}{
		{
			name:        "error handling structure test",
			description: "Verify that fetchAnalysisDataConcurrently method exists and handles errors properly",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal analyzer instance
			analyzer := &SonarAnalizerInteractor{}

			// Verify the method exists by checking it can be called
			// (This is primarily a compile-time check)

			// We can't easily test the full method without complex setup,
			// but we can verify the structure and that it compiles
			assert.NotNil(t, analyzer)
		})
	}
}

// TestProcessAnalysisRefactoredStructure tests the refactored processAnalysis method structure
func TestProcessAnalysisRefactoredStructure(t *testing.T) {
	tests := []struct {
		name        string
		description string
	}{
		{
			name:        "refactored method structure",
			description: "Verify that processAnalysis has been properly refactored into smaller functions",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal analyzer instance
			analyzer := &SonarAnalizerInteractor{}

			// Verify that the helper methods exist and are accessible
			// This is a structural test to ensure the refactoring was successful

			// Test populateAnalysisData method with various data types
			analysis := &model.SonarqubeAnalysis{}

			// Test with empty data
			emptyMeasures := sonarqube.GetMeasuresResponse{}
			emptyIssues := sonarqube.IssuesResponse{}
			analyzer.populateAnalysisData(analysis, emptyMeasures, emptyIssues)

			assert.NotNil(t, analysis.Measures)
			assert.Equal(t, 0, analysis.IssuesTotal)
			assert.Equal(t, 0, analysis.IssuesEffortTotal)

			// Test with populated data
			populatedMeasures := sonarqube.GetMeasuresResponse{
				Component: struct {
					Key       string `json:"key"`
					Name      string `json:"name"`
					Qualifier string `json:"qualifier"`
					Measures  []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					} `json:"measures"`
				}{
					Key:       "populated-project",
					Name:      "Populated Project",
					Qualifier: "TRK",
					Measures: []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					}{
						{Metric: "coverage", Value: "85.5"},
						{Metric: "bugs", Value: "3"},
					},
				},
			}

			populatedIssues := sonarqube.IssuesResponse{
				Total:       42,
				EffortTotal: 180,
			}

			// Reset analysis for second test
			analysis = &model.SonarqubeAnalysis{}
			analyzer.populateAnalysisData(analysis, populatedMeasures, populatedIssues)

			assert.NotNil(t, analysis.Measures)
			assert.Equal(t, 42, analysis.IssuesTotal)
			assert.Equal(t, 180, analysis.IssuesEffortTotal)

			// Verify measures data structure
			measuresData, ok := analysis.Measures["data"]
			assert.True(t, ok)
			measures, ok := measuresData.(sonarqube.GetMeasuresResponse)
			assert.True(t, ok)
			assert.Equal(t, "populated-project", measures.Component.Key)
			assert.Equal(t, "Populated Project", measures.Component.Name)
			assert.Len(t, measures.Component.Measures, 2)
		})
	}
}

// TestPopulateAnalysisDataEdgeCases tests edge cases for the populateAnalysisData method
func TestPopulateAnalysisDataEdgeCases(t *testing.T) {
	tests := []struct {
		name                string
		measures            sonarqube.GetMeasuresResponse
		sonarIssuesResponse sonarqube.IssuesResponse
		expectedTotal       int
		expectedEffort      int
	}{
		{
			name: "negative values handled correctly",
			measures: sonarqube.GetMeasuresResponse{
				Component: struct {
					Key       string `json:"key"`
					Name      string `json:"name"`
					Qualifier string `json:"qualifier"`
					Measures  []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					} `json:"measures"`
				}{
					Key:       "negative-project",
					Name:      "Negative Project",
					Qualifier: "TRK",
				},
			},
			sonarIssuesResponse: sonarqube.IssuesResponse{
				Total:       -1, // Edge case: negative values
				EffortTotal: -5,
			},
			expectedTotal:  -1,
			expectedEffort: -5,
		},
		{
			name: "maximum integer values",
			measures: sonarqube.GetMeasuresResponse{
				Component: struct {
					Key       string `json:"key"`
					Name      string `json:"name"`
					Qualifier string `json:"qualifier"`
					Measures  []struct {
						Metric string `json:"metric"`
						Value  string `json:"value"`
					} `json:"measures"`
				}{
					Key:       "max-project",
					Name:      "Max Project",
					Qualifier: "TRK",
				},
			},
			sonarIssuesResponse: sonarqube.IssuesResponse{
				Total:       2147483647, // Max int32
				EffortTotal: 2147483647,
			},
			expectedTotal:  2147483647,
			expectedEffort: 2147483647,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a minimal analyzer instance for testing
			analyzer := &SonarAnalizerInteractor{}

			// Create a test analysis object
			analysis := &model.SonarqubeAnalysis{}

			// Call the method under test
			analyzer.populateAnalysisData(analysis, tt.measures, tt.sonarIssuesResponse)

			// Verify the results
			assert.NotNil(t, analysis.Measures)
			assert.Equal(t, tt.expectedTotal, analysis.IssuesTotal)
			assert.Equal(t, tt.expectedEffort, analysis.IssuesEffortTotal)

			// Verify measures data structure
			measuresData, ok := analysis.Measures["data"]
			assert.True(t, ok, "Measures should contain 'data' key")
			assert.Equal(t, tt.measures, measuresData, "Measures data should match input")
		})
	}
}

// TestProcessAnalysisHelperFunctionsCoverage tests the helper functions for better coverage
func TestProcessAnalysisHelperFunctionsCoverage(t *testing.T) {
	t.Run("populateAnalysisData with complex measures", func(t *testing.T) {
		analyzer := &SonarAnalizerInteractor{}
		analysis := &model.SonarqubeAnalysis{}

		// Create complex measures with multiple metrics
		complexMeasures := sonarqube.GetMeasuresResponse{
			Component: struct {
				Key       string `json:"key"`
				Name      string `json:"name"`
				Qualifier string `json:"qualifier"`
				Measures  []struct {
					Metric string `json:"metric"`
					Value  string `json:"value"`
				} `json:"measures"`
			}{
				Key:       "complex-project-key",
				Name:      "Complex Project Name",
				Qualifier: "TRK",
				Measures: []struct {
					Metric string `json:"metric"`
					Value  string `json:"value"`
				}{
					{Metric: "coverage", Value: "85.5"},
					{Metric: "bugs", Value: "3"},
					{Metric: "vulnerabilities", Value: "1"},
					{Metric: "code_smells", Value: "15"},
					{Metric: "duplicated_lines_density", Value: "2.3"},
					{Metric: "ncloc", Value: "1500"},
					{Metric: "complexity", Value: "250"},
				},
			},
		}

		complexIssues := sonarqube.IssuesResponse{
			Total:       42,
			EffortTotal: 180,
		}

		// Test the method
		analyzer.populateAnalysisData(analysis, complexMeasures, complexIssues)

		// Verify all data is properly set
		assert.NotNil(t, analysis.Measures)
		assert.Equal(t, 42, analysis.IssuesTotal)
		assert.Equal(t, 180, analysis.IssuesEffortTotal)

		// Verify complex measures structure
		measuresData, ok := analysis.Measures["data"]
		assert.True(t, ok)
		measures, ok := measuresData.(sonarqube.GetMeasuresResponse)
		assert.True(t, ok)
		assert.Equal(t, "complex-project-key", measures.Component.Key)
		assert.Equal(t, "Complex Project Name", measures.Component.Name)
		assert.Equal(t, "TRK", measures.Component.Qualifier)
		assert.Len(t, measures.Component.Measures, 7)

		// Verify specific metrics
		foundCoverage := false
		foundBugs := false
		for _, measure := range measures.Component.Measures {
			if measure.Metric == "coverage" && measure.Value == "85.5" {
				foundCoverage = true
			}
			if measure.Metric == "bugs" && measure.Value == "3" {
				foundBugs = true
			}
		}
		assert.True(t, foundCoverage, "Coverage metric should be found")
		assert.True(t, foundBugs, "Bugs metric should be found")
	})

	t.Run("populateAnalysisData preserves all data integrity", func(t *testing.T) {
		analyzer := &SonarAnalizerInteractor{}
		analysis := &model.SonarqubeAnalysis{}

		// Test with specific data that should be preserved exactly
		measures := sonarqube.GetMeasuresResponse{
			Component: struct {
				Key       string `json:"key"`
				Name      string `json:"name"`
				Qualifier string `json:"qualifier"`
				Measures  []struct {
					Metric string `json:"metric"`
					Value  string `json:"value"`
				} `json:"measures"`
			}{
				Key:       "integrity-test-project",
				Name:      "Data Integrity Test Project",
				Qualifier: "TRK",
			},
		}

		issues := sonarqube.IssuesResponse{
			Total:       123,
			EffortTotal: 456,
		}

		// Call the method
		analyzer.populateAnalysisData(analysis, measures, issues)

		// Verify exact data preservation
		assert.Equal(t, 123, analysis.IssuesTotal)
		assert.Equal(t, 456, analysis.IssuesEffortTotal)

		// Verify measures map structure
		assert.Contains(t, analysis.Measures, "data")
		storedMeasures := analysis.Measures["data"].(sonarqube.GetMeasuresResponse)
		assert.Equal(t, measures.Component.Key, storedMeasures.Component.Key)
		assert.Equal(t, measures.Component.Name, storedMeasures.Component.Name)
		assert.Equal(t, measures.Component.Qualifier, storedMeasures.Component.Qualifier)
	})
}

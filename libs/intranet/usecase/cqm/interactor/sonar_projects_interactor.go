// Package interactor provides business logic for SonarQube project management.
// This file handles SonarQube project creation, validation, and integration with Jira projects.
package interactor

import (
	"context"
	"errors"
	"fmt"
	"time"

	corerepository "sa-intranet/core/repository"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

// SonarProjectsData represents the response structure for SonarQube project listings.
// It includes both the project data and pagination metadata.
type SonarProjectsData struct {
	// SonarProjects contains the list of SonarQube projects for the current page
	SonarProjects []SonarProject `json:"sonarProjects"`
	// Pagination contains metadata about the current page and total count
	Pagination Pagination `json:"pagination"`
}

type SonarProject struct {
	ID          uuid.UUID    `json:"id"`
	ProjectName string       `json:"projectName"`
	Active      bool         `json:"active"`
	JiraProject *JiraProject `json:"jiraProject,omitempty"`
	ProjectKey  string       `json:"projectKey"`
	Branch      string       `json:"branch"`
	CreatedAt   time.Time    `json:"createdAt"`
	UpdatedAt   time.Time    `json:"updatedAt"`
}

type SonarProjectsInteractor struct {
	sonarProjectRepo repository.SonarqubeProjectRepository
	validator        *validator.Validate
	sonarqube        *sonarqube.Client
	customValidator  *validatorext.CustomValidator
}

type SonarProjectValidator struct {
	ProjectKey    string    `json:"projectKey" validate:"required"`
	JiraProjectID uuid.UUID `json:"jiraProjectId" validate:"required,jira_project_exists"`
	Branch        string    `json:"branch" validate:"required"`
	Active        *bool     `json:"active" validate:"required"`
}

func NewSonarProjectsInteractor(i *do.Injector) (*SonarProjectsInteractor, error) {
	sonarqubeClient, err := do.Invoke[*sonarqube.Client](i)
	if err != nil {
		return nil, err
	}

	sonarProjectRepo, err := do.Invoke[repository.SonarqubeProjectRepository](i)
	if err != nil {
		return nil, err
	}

	validator, err := do.Invoke[*validator.Validate](i)
	if err != nil {
		return nil, err
	}

	customValidator := do.MustInvoke[*validatorext.CustomValidator](i)

	return &SonarProjectsInteractor{
		sonarqube:        sonarqubeClient,
		sonarProjectRepo: sonarProjectRepo,
		validator:        validator,
		customValidator:  customValidator,
	}, nil
}

func (i *SonarProjectsInteractor) InitialData(filter string, page int, pageSize int) (SonarProjectsData, error) {
	var data SonarProjectsData

	ctx := context.Background()

	params := i.buildPaginationParams(filter, page, pageSize)

	sonarProjectsResp, err := i.sonarProjectRepo.List(ctx, params)
	if err != nil {
		return data, err
	}

	sonarProjects := i.transformSonarProjects(sonarProjectsResp.Items)
	pagination := i.buildPagination(sonarProjectsResp)

	return SonarProjectsData{
		SonarProjects: sonarProjects,
		Pagination:    pagination,
	}, nil
}

// buildPaginationParams creates pagination parameters for the repository query
func (i *SonarProjectsInteractor) buildPaginationParams(filter string, page, pageSize int) corerepository.PaginationParams[repository.SonarqubeProjectFilter] {
	return corerepository.PaginationParams[repository.SonarqubeProjectFilter]{
		Filters: repository.SonarqubeProjectFilter{
			Name: filter,
		},
		Page:     page,
		PageSize: pageSize,
	}
}

// transformSonarProjects converts repository models to interactor models
func (i *SonarProjectsInteractor) transformSonarProjects(projects []model.SonarqubeProject) []SonarProject {
	sonarProjects := make([]SonarProject, 0, len(projects))

	for _, project := range projects {
		sonarProject := i.transformSingleSonarProject(project)
		sonarProjects = append(sonarProjects, sonarProject)
	}

	return sonarProjects
}

// transformSingleSonarProject converts a single repository model to interactor model
func (i *SonarProjectsInteractor) transformSingleSonarProject(project model.SonarqubeProject) SonarProject {
	companyClient := i.transformCompanyClient(project.JiraProject)
	jiraProject := i.transformJiraProject(project.JiraProject, companyClient)

	return SonarProject{
		ID:          project.ID,
		ProjectName: project.ProjectName,
		ProjectKey:  project.ProjectKey,
		Active:      project.Active,
		Branch:      project.Branch,
		CreatedAt:   project.CreatedAt,
		UpdatedAt:   project.UpdatedAt,
		JiraProject: jiraProject,
	}
}

// transformCompanyClient converts company client from repository model
func (i *SonarProjectsInteractor) transformCompanyClient(jiraProject *model.JiraProject) *CompanyClient {
	if jiraProject == nil || jiraProject.CompanyClient == nil {
		return nil
	}

	return &CompanyClient{
		ID:   jiraProject.CompanyClient.ID,
		Name: jiraProject.CompanyClient.Name,
	}
}

// transformJiraProject converts jira project from repository model
func (i *SonarProjectsInteractor) transformJiraProject(jiraProject *model.JiraProject, companyClient *CompanyClient) *JiraProject {
	if jiraProject == nil {
		return nil
	}

	return &JiraProject{
		ID:            jiraProject.ID,
		Name:          jiraProject.Name,
		JiraURL:       jiraProject.JiraURL,
		Active:        jiraProject.Active,
		Username:      jiraProject.Username,
		ProjectKey:    jiraProject.ProjectKey,
		CompanyClient: companyClient,
	}
}

// buildPagination creates pagination info from repository response
func (i *SonarProjectsInteractor) buildPagination(resp *corerepository.PaginatedResult[model.SonarqubeProject]) Pagination {
	return Pagination{
		TotalCount:  int(resp.TotalItems),
		CurrentPage: resp.Page,
		PerPage:     resp.PageSize,
	}
}

func (i *SonarProjectsInteractor) CreateSonarProject(jiraProject SonarProjectValidator) (SonarProject, map[string]any, error) {
	ctx := context.Background()
	sonarProjectResp := SonarProject{}

	validationErrors, err := i.validateSonarProject(jiraProject)
	if err != nil {
		return sonarProjectResp, validationErrors, err
	}

	projectResp, err := i.sonarqube.GetProject(jiraProject.ProjectKey)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	project := i.buildSonarProjectModel(jiraProject, projectResp.Name)

	sonarProjectFromDB, err := i.saveAndRetrieveProject(ctx, project)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	return i.buildSonarProjectResponse(sonarProjectFromDB), nil, nil
}

func (i *SonarProjectsInteractor) buildSonarProjectModel(validator SonarProjectValidator, projectName string) *model.SonarqubeProject {
	return &model.SonarqubeProject{
		ProjectKey:    validator.ProjectKey,
		Branch:        validator.Branch,
		ProjectName:   projectName,
		JiraProjectID: validator.JiraProjectID,
		Active:        *validator.Active,
	}
}

func (i *SonarProjectsInteractor) saveAndRetrieveProject(ctx context.Context, project *model.SonarqubeProject) (*model.SonarqubeProject, error) {
	sonarProjectFromDB, saveErr := i.sonarProjectRepo.Save(ctx, project, project.ID == uuid.Nil)
	if saveErr != nil {
		return nil, saveErr
	}

	return i.sonarProjectRepo.Find(ctx, sonarProjectFromDB.ID)
}

func (i *SonarProjectsInteractor) UpdateSonarProject(projectID string, sonarProject SonarProjectValidator) (SonarProject, map[string]any, error) {
	ctx := context.Background()
	sonarProjectResp := SonarProject{}

	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		return sonarProjectResp, nil, fmt.Errorf("failed to parse project ID: %w", err)
	}

	sonarProjectFromDB, err := i.sonarProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	validationErrors, err := i.validateSonarProject(sonarProject)
	if err != nil {
		return sonarProjectResp, validationErrors, err
	}

	updatedProject, err := i.buildUpdatedProject(projectUUID, sonarProject, sonarProjectFromDB)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	return i.saveAndReturnUpdatedProject(ctx, updatedProject)
}

func (i *SonarProjectsInteractor) validateSonarProject(sonarProject SonarProjectValidator) (map[string]any, error) {
	err := i.validator.Struct(sonarProject)
	if err != nil {
		var validationErr validator.ValidationErrors

		ok := errors.As(err, &validationErr)

		if !ok {
			return nil, fmt.Errorf("failed to validate sonar project: %w", err)
		}

		validationErrors := validatorext.FormatFormErrors(sonarProject, validationErr)

		return validationErrors, ErrValidationFailed
	}

	return nil, nil
}

func (i *SonarProjectsInteractor) buildUpdatedProject(
	projectUUID uuid.UUID,
	sonarProject SonarProjectValidator,
	existingProject *model.SonarqubeProject,
) (*model.SonarqubeProject, error) {
	project := &model.SonarqubeProject{
		ID:            projectUUID,
		Branch:        sonarProject.Branch,
		ProjectName:   existingProject.ProjectName,
		JiraProjectID: sonarProject.JiraProjectID,
		Active:        *sonarProject.Active,
		ProjectKey:    sonarProject.ProjectKey,
		CreatedAt:     existingProject.CreatedAt,
		UpdatedAt:     existingProject.UpdatedAt,
	}

	if sonarProject.ProjectKey != existingProject.ProjectKey {
		projectResponse, err := i.sonarqube.GetProject(sonarProject.ProjectKey)
		if err != nil {
			return nil, err
		}

		project.ProjectName = projectResponse.Name
	}

	return project, nil
}

func (i *SonarProjectsInteractor) saveAndReturnUpdatedProject(
	ctx context.Context,
	project *model.SonarqubeProject,
) (SonarProject, map[string]any, error) {
	sonarProjectResp := SonarProject{}

	savedProject, saveErr := i.sonarProjectRepo.Save(ctx, project, false)
	if saveErr != nil {
		return sonarProjectResp, nil, saveErr
	}

	// Use the ID from the saved project (in case it was generated)
	sonarProjectFromDB, err := i.sonarProjectRepo.Find(ctx, savedProject.ID)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	return i.buildSonarProjectResponse(sonarProjectFromDB), nil, nil
}

func (i *SonarProjectsInteractor) buildSonarProjectResponse(sonarProjectFromDB *model.SonarqubeProject) SonarProject {
	var companyClient *CompanyClient
	if sonarProjectFromDB.JiraProject != nil && sonarProjectFromDB.JiraProject.CompanyClient != nil {
		companyClient = &CompanyClient{
			ID:   sonarProjectFromDB.JiraProject.CompanyClient.ID,
			Name: sonarProjectFromDB.JiraProject.CompanyClient.Name,
		}
	}

	var jiraProject *JiraProject
	if sonarProjectFromDB.JiraProject != nil {
		jiraProject = &JiraProject{
			ID:            sonarProjectFromDB.JiraProject.ID,
			Active:        sonarProjectFromDB.JiraProject.Active,
			Name:          sonarProjectFromDB.JiraProject.Name,
			JiraURL:       sonarProjectFromDB.JiraProject.JiraURL,
			ProjectKey:    sonarProjectFromDB.JiraProject.ProjectKey,
			Username:      sonarProjectFromDB.JiraProject.Username,
			CompanyClient: companyClient,
		}
	}

	return SonarProject{
		ID:          sonarProjectFromDB.ID,
		ProjectName: sonarProjectFromDB.ProjectName,
		ProjectKey:  sonarProjectFromDB.ProjectKey,
		JiraProject: jiraProject,
		Active:      sonarProjectFromDB.Active,
		Branch:      sonarProjectFromDB.Branch,
		CreatedAt:   sonarProjectFromDB.CreatedAt,
		UpdatedAt:   sonarProjectFromDB.UpdatedAt,
	}
}

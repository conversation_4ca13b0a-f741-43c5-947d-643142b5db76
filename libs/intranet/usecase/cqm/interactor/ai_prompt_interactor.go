// Package interactor provides business logic interactors for the Code Quality Management (CQM) system.
// This file specifically handles AI prompt generation for SonarQube issues.
package interactor

import (
	"bytes"
	"context"
	"embed"
	"errors"
	"fmt"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"
	"strings"
	"text/template"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

// Error definitions for AI prompt generation operations
var (
	// ErrAIProjectNotFound is returned when the specified project cannot be found
	ErrAIProjectNotFound = errors.New("project not found")
	// ErrAIInvalidIssueKeys is returned when invalid issue keys are provided
	ErrAIInvalidIssueKeys = errors.New("invalid issue keys provided")
	// ErrAIIssuesNotFound is returned when some issues are not found in SonarQube
	ErrAIIssuesNotFound = errors.New("some issues not found in SonarQube")
	// ErrAIPromptGeneration is returned when prompt generation fails
	ErrAIPromptGeneration = errors.New("failed to generate AI prompt")
	// ErrAITooManyIssues is returned when too many issues are requested
	ErrAITooManyIssues = errors.New("too many issues requested, maximum 50 allowed")
)

// Constants for AI prompt generation
const (
	// MaxIssuesPerRequest defines the maximum number of issues that can be processed in a single request
	MaxIssuesPerRequest = 50
)

//go:embed templates/ai_prompt.tmpl
var aiPromptTemplateFS embed.FS

// PromptTemplateData contains all data needed for the AI prompt template
type PromptTemplateData struct {
	ProjectInfo ProjectInfo   `json:"projectInfo"`
	Issues      []IssueDetail `json:"issues"`
	IssueCount  int           `json:"issueCount"`
}

// GenerateAIPromptRequest represents the input for AI prompt generation
type GenerateAIPromptRequest struct {
	// ProjectID is the UUID of the SonarQube project
	ProjectID string `json:"projectId" validate:"required,uuid"`
	// IssueKeys is the list of SonarQube issue keys to include in the prompt
	IssueKeys []string `json:"issueKeys" validate:"required,min=1,dive,required"`
}

// ProjectInfo contains basic project information for the AI prompt
type ProjectInfo struct {
	// Name is the project name
	Name string `json:"name"`
	// Key is the SonarQube project key
	Key string `json:"key"`
	// Branch is the project branch
	Branch string `json:"branch"`
	// JiraProject contains Jira project information
	JiraProject JiraProjectInfo `json:"jiraProject"`
}

// JiraProjectInfo contains Jira project information
type JiraProjectInfo struct {
	// Name is the Jira project name
	Name string `json:"name"`
	// Key is the Jira project key
	Key string `json:"key"`
}

// IssueDetail contains detailed information about a SonarQube issue for AI prompt generation
type IssueDetail struct {
	// Key is the unique SonarQube issue key
	Key string `json:"key"`
	// Rule is the SonarQube rule that was violated
	Rule string `json:"rule"`
	// Severity indicates the issue severity (INFO, MINOR, MAJOR, CRITICAL, BLOCKER)
	Severity string `json:"severity"`
	// Type indicates the issue type (CODE_SMELL, BUG, VULNERABILITY, SECURITY_HOTSPOT)
	Type string `json:"type"`
	// Message is the issue description
	Message string `json:"message"`
	// Component is the file or component where the issue occurs
	Component string `json:"component"`
	// Line is the line number where the issue occurs (if applicable)
	Line int `json:"line,omitempty"`
	// Effort is the estimated effort to fix the issue
	Effort string `json:"effort,omitempty"`
	// Tags are the issue tags
	Tags []string `json:"tags"`
	// Author is the author of the code that caused the issue
	Author string `json:"author,omitempty"`
	// SonarURL is the direct link to the issue in SonarQube
	SonarURL string `json:"sonarUrl"`
	// CreationDate is when the issue was first detected
	CreationDate time.Time `json:"creationDate"`
	// UpdateDate is when the issue was last updated
	UpdateDate time.Time `json:"updateDate"`
}

// AIPromptResponse represents the output of AI prompt generation
type AIPromptResponse struct {
	// Prompt is the generated AI prompt text
	Prompt string `json:"prompt"`
	// IssueCount is the number of issues included in the prompt
	IssueCount int `json:"issueCount"`
	// ProjectInfo contains project information
	ProjectInfo ProjectInfo `json:"projectInfo"`
	// Issues contains detailed information about each issue
	Issues []IssueDetail `json:"issues"`
	// GeneratedAt is the timestamp when the prompt was generated
	GeneratedAt time.Time `json:"generatedAt"`
}

// AIPromptInteractor handles AI prompt generation for SonarQube issues
type AIPromptInteractor struct {
	// sonarqube is the SonarQube client for fetching issue details
	sonarqube *sonarqube.Client
	// sonarProjectRepo is the repository for SonarQube project operations
	sonarProjectRepo repository.SonarqubeProjectRepository
	// sonarIssueRepo is the repository for SonarQube issue operations
	sonarIssueRepo repository.SonarqubeIssueRepository
	// validator is used for input validation
	validator *validator.Validate
	// sonarConfig contains SonarQube configuration
	sonarConfig sonarqube.ClientConfig
	// promptTemplate is the parsed template for generating AI prompts
	promptTemplate *template.Template
}

// NewAIPromptInteractor creates a new AI prompt interactor with dependency injection
func NewAIPromptInteractor(i *do.Injector) (*AIPromptInteractor, error) {
	sonarqubeClient, err := do.Invoke[*sonarqube.Client](i)
	if err != nil {
		return nil, fmt.Errorf("failed to inject SonarQube client: %w", err)
	}

	sonarProjectRepo, err := do.Invoke[repository.SonarqubeProjectRepository](i)
	if err != nil {
		return nil, fmt.Errorf("failed to inject SonarQube project repository: %w", err)
	}

	sonarIssueRepo, err := do.Invoke[repository.SonarqubeIssueRepository](i)
	if err != nil {
		return nil, fmt.Errorf("failed to inject SonarQube issue repository: %w", err)
	}

	validator, err := do.Invoke[*validator.Validate](i)
	if err != nil {
		return nil, fmt.Errorf("failed to inject validator: %w", err)
	}

	sonarConfig, err := do.Invoke[sonarqube.ClientConfig](i)
	if err != nil {
		return nil, fmt.Errorf("failed to inject SonarQube config: %w", err)
	}

	// Load and parse the template from embedded file system
	templateContent, err := aiPromptTemplateFS.ReadFile("templates/ai_prompt.tmpl")
	if err != nil {
		return nil, fmt.Errorf("failed to read AI prompt template: %w", err)
	}

	tmpl, err := template.New("aiPrompt").Funcs(template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"join": strings.Join,
	}).Parse(string(templateContent))
	if err != nil {
		return nil, fmt.Errorf("failed to parse AI prompt template: %w", err)
	}

	return &AIPromptInteractor{
		sonarqube:        sonarqubeClient,
		sonarProjectRepo: sonarProjectRepo,
		sonarIssueRepo:   sonarIssueRepo,
		validator:        validator,
		sonarConfig:      sonarConfig,
		promptTemplate:   tmpl,
	}, nil
}

// GeneratePrompt generates an AI prompt for fixing the specified SonarQube issues
func (ai *AIPromptInteractor) GeneratePrompt(ctx context.Context, req GenerateAIPromptRequest) (AIPromptResponse, error) {
	// Validate input
	if err := ai.validateRequest(req); err != nil {
		return AIPromptResponse{}, fmt.Errorf("validation failed: %w", err)
	}

	// Get project information
	project, err := ai.getProject(ctx, req.ProjectID)
	if err != nil {
		return AIPromptResponse{}, fmt.Errorf("failed to get project: %w", err)
	}

	// Fetch issues from SonarQube
	issues, err := ai.fetchIssues(project.ProjectKey, req.IssueKeys)
	if err != nil {
		return AIPromptResponse{}, fmt.Errorf("failed to fetch issues: %w", err)
	}

	// Build project info
	projectInfo := ai.buildProjectInfo(project)

	// Convert SonarQube issues to detailed format
	issueDetails := ai.convertToIssueDetails(issues, project.ProjectKey)

	// Generate the AI prompt
	prompt := ai.generatePromptText(projectInfo, issueDetails)

	return AIPromptResponse{
		Prompt:      prompt,
		IssueCount:  len(issueDetails),
		ProjectInfo: projectInfo,
		Issues:      issueDetails,
		GeneratedAt: time.Now(),
	}, nil
}

// validateRequest validates the input request
func (ai *AIPromptInteractor) validateRequest(req GenerateAIPromptRequest) error {
	if err := ai.validator.Struct(req); err != nil {
		return fmt.Errorf("validation error: %w", err)
	}

	if len(req.IssueKeys) > MaxIssuesPerRequest {
		return ErrAITooManyIssues
	}

	// Validate issue keys format (basic validation)
	for _, key := range req.IssueKeys {
		if strings.TrimSpace(key) == "" {
			return ErrAIInvalidIssueKeys
		}
	}

	return nil
}

// getProject retrieves the SonarQube project from the database
func (ai *AIPromptInteractor) getProject(ctx context.Context, projectID string) (*model.SonarqubeProject, error) {
	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		return nil, fmt.Errorf("invalid project ID format: %w", err)
	}

	project, err := ai.sonarProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", ErrAIProjectNotFound, projectID)
	}

	return project, nil
}

// fetchIssues retrieves issues from SonarQube API
func (ai *AIPromptInteractor) fetchIssues(projectKey string, issueKeys []string) ([]sonarqube.Issue, error) {
	filter := &sonarqube.IssueFilter{
		Issues: issueKeys,
	}
	pagination := &sonarqube.PaginationOptions{
		PageIndex: 1,
		PageSize:  len(issueKeys),
	}

	issuesResp, err := ai.sonarqube.GetProjectIssues(projectKey, filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch issues from SonarQube: %w", err)
	}

	// Verify all requested issues were found
	if len(issuesResp.Issues) != len(issueKeys) {
		return nil, fmt.Errorf("%w: requested %d, found %d", ErrAIIssuesNotFound, len(issueKeys), len(issuesResp.Issues))
	}

	return issuesResp.Issues, nil
}

// buildProjectInfo creates project information for the response
func (ai *AIPromptInteractor) buildProjectInfo(project *model.SonarqubeProject) ProjectInfo {
	jiraInfo := JiraProjectInfo{}
	if project.JiraProject != nil {
		jiraInfo.Name = project.JiraProject.Name
		jiraInfo.Key = project.JiraProject.ProjectKey
	}

	return ProjectInfo{
		Name:        project.ProjectName,
		Key:         project.ProjectKey,
		Branch:      project.Branch,
		JiraProject: jiraInfo,
	}
}

// convertToIssueDetails converts SonarQube issues to detailed format for AI prompt
func (ai *AIPromptInteractor) convertToIssueDetails(issues []sonarqube.Issue, projectKey string) []IssueDetail {
	details := make([]IssueDetail, 0, len(issues))

	for _, issue := range issues {
		detail := IssueDetail{
			Key:          issue.Key,
			Rule:         issue.Rule,
			Severity:     issue.Severity,
			Type:         issue.Type,
			Message:      issue.Message,
			Component:    issue.Component,
			Line:         issue.Line,
			Effort:       issue.Effort,
			Tags:         issue.Tags,
			Author:       issue.Author,
			SonarURL:     ai.buildSonarURL(issue.Key, projectKey),
			CreationDate: issue.CreationDate.Time,
			UpdateDate:   issue.UpdateDate.Time,
		}
		details = append(details, detail)
	}

	return details
}

// buildSonarURL creates a direct link to the issue in SonarQube
func (ai *AIPromptInteractor) buildSonarURL(issueKey, projectKey string) string {
	return fmt.Sprintf("%s/project/issues?id=%s&open=%s", ai.sonarConfig.URL, projectKey, issueKey)
}

// generatePromptText creates the AI prompt text using the template
func (ai *AIPromptInteractor) generatePromptText(projectInfo ProjectInfo, issues []IssueDetail) string {
	data := PromptTemplateData{
		ProjectInfo: projectInfo,
		Issues:      issues,
		IssueCount:  len(issues),
	}

	var buf bytes.Buffer
	if err := ai.promptTemplate.Execute(&buf, data); err != nil {
		// Fallback to a basic prompt if template execution fails
		return fmt.Sprintf("Error generating prompt: %v\n\nProject: %s\nIssues: %d",
			err, projectInfo.Name, len(issues))
	}

	return buf.String()
}

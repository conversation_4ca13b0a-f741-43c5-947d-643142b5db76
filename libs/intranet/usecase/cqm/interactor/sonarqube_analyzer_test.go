package interactor_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/samber/do"
)

func setupSonarqubeGetMeasuresServer() *httptest.Server {
	jsonDefaultSuccess, err := testhelpers.FixtureFS.ReadFile("testdata/sonarqube/measures-default-success-response.json")
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Handle different API endpoints
		switch r.URL.Path {
		case "/api/measures/component":
			if err != nil {
				fmt.Printf("failed to read fixture file: %v", err)
			}

			w.<PERSON><PERSON>().Set("Content-Type", "application/json")

			_, err := w.Write(jsonDefaultSuccess)
			if err != nil {
				fmt.Printf("failed to write response: %v", err)
			}

			return
		case "/api/issues/search":
			w<PERSON><PERSON>(http.StatusOK)

			response := sonarqube.IssuesResponse{
				Total:       1,
				Ps:          1,
				P:           1,
				EffortTotal: 60,
				Issues: []sonarqube.Issue{
					{
						Key:       "key1",
						Component: "component1",
						TextRange: sonarqube.TextRange{
							StartLine: 1,
							EndLine:   2,
						},
					},
				},
			}
			jsonResponse, _ := json.Marshal(response)

			_, err := w.Write(jsonResponse)
			if err != nil {
				fmt.Printf("failed to write response: %v", err)
			}

			return
		default:
			// Default 404 response
			w.WriteHeader(http.StatusNotFound)

			return
		}
	}))

	return server
}

// TestSonarAnalizerInteractor tests the SonarAnalizerInteractor
// go test -v ./libs/intranet/usecase/cqm/interactor -run TestSonarAnalizerInteractor -count=1
func TestSonarAnalizerInteractor(t *testing.T) {
	t.Parallel()

	inj := injector.Clone()
	server := setupSonarqubeGetMeasuresServer()
	serverURL := server.URL

	do.Override(inj, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		config := sonarqube.ClientConfig{
			URL:   serverURL,
			Token: "test-token",
		}

		return config, nil
	})

	usecase, err := do.Invoke[*interactor.SonarAnalizerInteractor](inj)
	if err != nil {
		t.Fatalf("Failed to get SonarAnalizerInteractor: %v", err)
	}

	t.Run("SonarqubeAnalize.Analyze", func(t *testing.T) {
		ctx := context.Background()
		testcases := testGetSonarQubeWebhookCases(t)

		for _, tt := range testcases {
			options := interactor.AnalizeOptions{}
			_, err = usecase.Analyze(ctx, tt.input, options)

			gotError := err != nil
			gotNoError := err == nil
			expectError := tt.wantErr

			switch {
			case expectError && gotNoError:
				// Expected an error but got none
				t.Logf("Expected an error but got none")
			case !expectError && gotError:
				// expected no error, got no error
				t.Errorf("SonarAnalizer failed: %v", err)
			case gotError:
				t.Logf("Expected an error and got: %v", err)
			}

			t.Log("SonarqubeAnalize passed")
		}
	})
}

func TestFetchAnalysisDataConcurrently(t *testing.T) {
	t.Skip("Skipping integration test - requires complex setup with SonarQube client and database")
}

func TestPopulateAnalysisData(t *testing.T) {
	t.Skip("Skipping unit test - populateAnalysisData is unexported and requires complex setup")
}

# SonarQube Issues AI Fix Prompt

## Project Information
- **Project**: {{.ProjectInfo.Name}} ({{.ProjectInfo.Key}})
- **Branch**: {{.ProjectInfo.Branch}}
{{- if .ProjectInfo.JiraProject.Name}}
- **Jira Project**: {{.ProjectInfo.JiraProject.Name}} ({{.ProjectInfo.JiraProject.Key}})
{{- end}}
- **Total Issues**: {{.IssueCount}}

## Issues to Fix

{{- range $index, $issue := .Issues}}
### Issue {{add $index 1}}: {{$issue.Key}}
- **Severity**: {{$issue.Severity}}
- **Type**: {{$issue.Type}}
- **Message**: {{$issue.Message}}
- **Component**: {{$issue.Component}}
{{- if gt $issue.Line 0}}
- **Line**: {{$issue.Line}}
{{- end}}
- **Rule**: {{$issue.Rule}}
{{- if $issue.Effort}}
- **Effort**: {{$issue.Effort}}
{{- end}}
{{- if $issue.Tags}}
- **Tags**: {{join $issue.Tags ", "}}
{{- end}}
{{- if $issue.Author}}
- **Author**: {{$issue.Author}}
{{- end}}
- **SonarQube URL**: {{$issue.SonarURL}}
- **Created**: {{$issue.CreationDate.Format "2006-01-02 15:04:05"}}
- **Updated**: {{$issue.UpdateDate.Format "2006-01-02 15:04:05"}}

{{- end}}

## Instructions
Please analyze the above SonarQube issues and provide:
1. Root cause analysis for each issue
2. Specific code fixes with examples
3. Best practices to prevent similar issues
{{- if gt .IssueCount 1}}
4. Priority order for fixing (if multiple issues)
{{- end}}

## Guidelines
- Follow the project's existing code style and architecture
- Ensure fixes don't introduce new issues
- Consider performance implications
- Provide clear, actionable solutions
- Include code examples where applicable

package cqm

import (
	"sa-intranet/config"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/jira"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/go-playground/validator/v10"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

func Register(i *do.Injector, conf config.Config) error {
	do.Provide(i, func(i *do.Injector) (*validatorext.CustomValidator, error) {
		db := do.MustInvoke[*bun.DB](i)

		return &validatorext.CustomValidator{DB: db}, nil
	})

	do.Provide(i, func(i *do.Injector) (*validator.Validate, error) {
		validatorInstance := validator.New()
		customValidator := do.MustInvoke[*validatorext.CustomValidator](i)

		if validateErr := validatorInstance.RegisterValidationCtx(
			"company_client_exists",
			customValidator.ValidateModelExists("CompanyClient"),
		); validateErr != nil {
			return nil, validateErr
		}

		if validateErr := validatorInstance.RegisterValidationCtx(
			"jira_project_exists",
			customValidator.ValidateModelExists("JiraProject"),
		); validateErr != nil {
			return nil, validateErr
		}

		return validatorInstance, nil
	})

	// Register SonarQube client
	do.Provide(i, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		return conf.SonarQube, nil
	})

	do.Provide(i, sonarqube.New)

	// Register Jira client
	do.Provide(i, func(i *do.Injector) (jira.ClientConfig, error) {
		return conf.Jira, nil
	})

	do.Provide(i, jira.New)

	// Register repositories
	do.Provide(i, repository.NewJiraProjectDefaultRepository)
	do.Provide(i, repository.NewSonarqubeProjectDefaultRepository)
	do.Provide(i, repository.NewSonarqubeIssueDefaultRepository)
	do.Provide(i, repository.NewSonarqubeAnalysisDefaultRepository)
	do.Provide(i, repository.NewCompanyClientDefaultRepository)

	// Register interactors
	do.Provide(i, interactor.NewSonarIssuesInteractor)
	do.Provide(i, interactor.NewSonarProjectsInteractor)
	do.Provide(i, interactor.NewJiraProjectsInteractor)
	do.Provide(i, interactor.NewSonarAnalizerInteractor)
	do.Provide(i, interactor.NewCompanyClientsInteractor)
	do.Provide(i, interactor.NewAIPromptInteractor)

	return nil
}

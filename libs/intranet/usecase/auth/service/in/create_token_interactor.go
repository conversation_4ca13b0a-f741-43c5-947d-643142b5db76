package in

import (
	"context"
	"errors"
	"time"

	date "sa-intranet/core/date"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/samber/do"
)

// ErrEmptyJWTSecret is returned when JWT signing key is empty
var ErrEmptyJWTSecret = errors.New("JWT signing key cannot be empty")

type CreateTokenInput struct {
	UserID    uuid.UUID `json:"userId" validate:"required,user_exists"`
	ExpiresAt date.Date `json:"expiresAt" validate:"required"`
}

type CreateTokenOutput struct {
	ID        uuid.UUID `json:"id"`
	Token     string    `json:"token"`
	ExpiresAt date.Date `json:"expiresAt"`
	CreatedAt time.Time `json:"createdAt"`
}

type CreateTokenInPort interface {
	CreateToken(input CreateTokenInput) (CreateTokenOutput, error)
}

type CreateTokenInteractor struct {
	common.BaseTokenInteractor
}

func GenerateJWT(userID uuid.UUID, expiresAt date.Date, jwtSecret string) (string, error) {
	claims := jwt.MapClaims{
		"sub": userID.String(),  // subject claim with UUID
		"exp": expiresAt.Unix(), // expires at specified time
		"iat": time.Now().Unix(),
	}

	if len(jwtSecret) == 0 {
		return "", ErrEmptyJWTSecret
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	return token.SignedString([]byte(jwtSecret))
}

func NewCreateTokenInteractor(i *do.Injector) (CreateTokenInPort, error) {
	customValidator := do.MustInvoke[*validatorext.CustomValidator](i)
	repo := do.MustInvoke[repository.TokenRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())
	authConfig := do.MustInvoke[config.AuthConfig](i)

	if validateErr := validate.RegisterValidationCtx(
		"user_exists",
		customValidator.ValidateModelExists("User"),
	); validateErr != nil {
		return nil, validateErr
	}

	return &CreateTokenInteractor{
		BaseTokenInteractor: common.BaseTokenInteractor{
			Repo:         repo,
			Validate:     validate,
			JWTSecretKey: authConfig.JWTSecretKey,
		},
	}, nil
}

func (i *CreateTokenInteractor) CreateToken(input CreateTokenInput) (CreateTokenOutput, error) {
	var output CreateTokenOutput

	// Use common validation
	err := common.ValidateTokenInput(i.Validate, input)
	if err != nil {
		return output, err
	}

	tokenModel := i.mapInputToModel(input)

	// Generate JWT token
	jwtToken, err := GenerateJWT(input.UserID, input.ExpiresAt, i.JWTSecretKey)
	if err != nil {
		return output, err
	}

	tokenModel.Token = jwtToken

	// Use base interactor to save
	user, err := i.SaveToken(context.Background(), tokenModel, true)
	if err != nil {
		return output, err
	}

	output = i.mapTokenToOutput(user)

	return output, nil
}

func (i *CreateTokenInteractor) mapTokenToOutput(token *model.Token) CreateTokenOutput {
	return CreateTokenOutput{
		ID:        token.ID,
		Token:     token.Token,
		ExpiresAt: date.Date{Time: token.ExpiresAt},
		CreatedAt: token.CreatedAt,
	}
}

func (i *CreateTokenInteractor) mapInputToModel(input CreateTokenInput) *model.Token {
	return &model.Token{
		ExpiresAt: input.ExpiresAt.Time,
		UserID:    input.UserID,
	}
}

package config

import (
	"log"
	"os"

	"sa-intranet/core"
	authConfig "sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/cqm/jira"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/spf13/viper"
)

type DatabaseConfig struct {
	DatabaseURL string `mapstructure:"APP_DATABASE_URL"`
	Debug       bool   `mapstructure:"APP_DATABASE_DEBUG"`
}

type Config struct {
	SonarQube       sonarqube.ClientConfig `mapstructure:",squash"` // <--- important!
	Jira            jira.ClientConfig      `mapstructure:",squash"`
	Database        DatabaseConfig         `mapstructure:",squash"`
	Cypher          core.CypherConfig      `mapstructure:",squash"`
	JiraURL         string                 `mapstructure:"APP_JIRA_URL"`
	DefaultPageSize int                    `mapstructure:"APP_DEFAULT_PAGE_SIZE"`
	Auth            authConfig.AuthConfig  `mapstructure:",squash"`
}

func NewConfig() (*Config, error) {
	v := viper.New()

	// Get config path from env or fallback to cwd
	configPath := os.Getenv("APP_CONFIG_PATH")
	if configPath == "" {
		configPath, _ = os.Getwd() // Fallback to cwd if CONFIG_PATH is unset
	}

	// Set default values (very important, make sure to set all the vars)
	v.SetDefault("APP_SONARQUBE_URL", "http://localhost:9000")
	v.SetDefault("APP_SONARQUBE_TOKEN", "changeme")
	v.SetDefault("APP_DATABASE_URL", "changeme")
	v.SetDefault("APP_CYPHER_KEY", "changemePazRr4JPjKSe4hMTfddW1SR34zshZHAId/M=")
	v.SetDefault("APP_JIRA_URL", "changeme.atlassian.net")
	v.SetDefault("APP_DEFAULT_PAGE_SIZE", 15)
	v.SetDefault("APP_DATABASE_DEBUG", false)
	v.SetDefault("APP_AUTH_DEFAULT_ROLE", "guest")
	v.SetDefault("APP_AUTH_JWT_SECRET_KEY", "changeme")

	// Enable environment variables
	v.AutomaticEnv()

	// Read from .env file if exists
	v.SetConfigName(".env")
	v.SetConfigType("env")
	v.AddConfigPath(configPath) // Add current directory as config path

	// Ignore error if config file is not found
	if err := v.ReadInConfig(); err != nil {
		log.Printf("Could not read config file: %v\n", err)
	}

	config := &Config{}
	if err := v.Unmarshal(config); err != nil {
		return nil, err
	}

	return config, nil
}

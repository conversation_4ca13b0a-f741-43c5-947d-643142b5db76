# Required metadata
sonar.projectKey=sa-intranet-libs-intranet
sonar.projectName=SA Intranet - Libs Intranet
sonar.projectVersion=1.0

# Source code location
sonar.sources=.
sonar.inclusions=**/*.go
sonar.exclusions=**/vendor/**,**/tmp/**,**/testdata/**,**/*_test.go,**/*.yml,**/db/migrate/**,**/cli/**

# Test configuration
sonar.tests=.
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**,**/tmp/**,**/testdata/**

# Coverage configuration
sonar.coverage.exclusions=**/vendor/**,**/tmp/**,**/testdata/**


# Go specific settings
sonar.language=go
sonar.go.file.suffixes=.go
sonar.go.coverage.reportPaths=coverage/profile.out
sonar.go.golangci-lint.reportPaths=coverage/golangci-lint-report.xml
sonar.go.govet.reportPaths=coverage/govet-report.out
sonar.go.tests.reportPaths=coverage/tests-report.json


# Encoding of source files
sonar.sourceEncoding=UTF-8

# Analysis settings
sonar.analysis.mode=publish
sonar.verbose=true

# SCM settings
sonar.scm.provider=git
sonar.scm.disabled=false

# Quality Gate settings
sonar.qualitygate.wait=true

# Duplications
sonar.cpd.exclusions=**/*_test.go

# Debug settings - add these to help troubleshoot the coverage discrepancy
sonar.log.level=DEBUG
sonar.coverage.dtdVerification=false

# Ignore issues with the following rules (see https://rules.sonarsource.com/go)
# sonar.issue.ignore.multicriteria=e1,e2

# sonar.issue.ignore.multicriteria.e1.ruleKey=go:S3776 # Cognitive Complexity of functions should not be too high
# sonar.issue.ignore.multicriteria.e1.resourceKey=**/*.go

# sonar.issue.ignore.multicriteria.e1.ruleKey=go:S1192 # String literals should not be duplicated
# sonar.issue.ignore.multicriteria.e1.resourceKey=**/*.go

# Dependency Check Plugin Configuration
sonar.dependencyCheck.reportPath=../../devops/security/dependency-check-reports/dependency-check-report.xml
sonar.dependencyCheck.htmlReportPath=../../devops/security/dependency-check-reports/dependency-check-report.html
sonar.dependencyCheck.jsonReportPath=../../devops/security/dependency-check-reports/dependency-check-report.json

# Import security scan results in SARIF format
sonar.sarifReportPaths=../../devops/security/trivy-reports/trivy-results.sarif,../../devops/security/grype-reports/grype-results.sarif,../../devops/security/snyk-reports/snyk-results.sarif,../../devops/security/snyk-reports/snyk-code-results.sarif# Dependency Check Plugin Configuration
sonar.dependencyCheck.reportPath=../../devops/security/dependency-check-reports/dependency-check-report.xml
sonar.dependencyCheck.htmlReportPath=../../devops/security/dependency-check-reports/dependency-check-report.html
sonar.dependencyCheck.jsonReportPath=../../devops/security/dependency-check-reports/dependency-check-report.json

# Import security scan results in SARIF format
sonar.sarifReportPaths=../../devops/security/trivy-reports/trivy-results.sarif,../../devops/security/grype-reports/grype-results.sarif,../../devops/security/snyk-reports/snyk-results.sarif,../../devops/security/snyk-reports/snyk-code-results.sarif
# Multi-stage build for SonarQube CLI tool
FROM golang:1.25-alpine AS builder

# Set working directory
WORKDIR /app

# Install git (needed for go mod)
RUN apk add --no-cache git

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY main.go ./

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o sonar-cli main.go

# Final stage - minimal runtime image
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Create non-root user
RUN addgroup -g 1001 -S sonar && \
    adduser -u 1001 -S sonar -G sonar

# Set working directory
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/sonar-cli .

# Copy the entrypoint script
COPY entrypoint.sh .

# Create reports directory
RUN mkdir -p /app/reports && \
    chown -R sonar:sonar /app && \
    chmod +x entrypoint.sh

# Switch to non-root user
USER sonar

# Set the entrypoint
ENTRYPOINT ["./entrypoint.sh"]

# Default command shows help
CMD []

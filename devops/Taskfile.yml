# https://taskfile.dev

version: "3"

vars:
  FRONTEND_DIR: "./services/frontend"
  WIKI_DIR: "./services/wiki"
  HEXA_DIR: "./libs/hexa"
  INTRANET_DIR: "./libs/intranet"

includes:
  intranet:
    dir: "{{.INTRANET_DIR}}"
    taskfile: "{{.INTRANET_DIR}}/Taskfile.yml"
  hexa:
    dir: "{{.HEXA_DIR}}"
    taskfile: "{{.HEXA_DIR}}/Taskfile.yml"
  wiki:
    dir: "{{.WIKI_DIR}}"
    taskfile: "{{.WIKI_DIR}}/Taskfile.yml"
  frontend:
    dir: "{{.FRONTEND_DIR}}"	
    taskfile: "{{.FRONTEND_DIR}}/Taskfile.yml"

tasks:
  build:
    desc: "Build the project"
    cmds:
    - task: frontend:build-prod
  setup:
    desc: "Setup the project"
    cmds:
    - task: wiki:setup
    - task: wiki:build
    - task: frontend:setup
    - mkdir -p {{.FRONTEND_DIR}}/public/wiki
    - cp -r {{.WIKI_DIR}}/dist/* {{.FRONTEND_DIR}}/public/wiki/
  go-mod-rm-replace:
    cmds:
      - go mod edit -dropreplace github.com/emilioforrer/hexa@v0.0.0
    silent: true
  go-mod-add-replace:
    cmds:
      - go mod edit -replace=github.com/emilioforrer/hexa@v0.0.0=./hexa
    silent: true
  run-ci:
    cmds:
    - task: run-check
    - task: run-test
    - task: run-security-scan
    - task: run-sonarqube-scan
    silent: false
  run-check:
    cmds:
      - task: intranet:run-check
    silent: false
  run-test:
    cmds:
      - task: intranet:run-test
    env:
      GO_TEST_TIMEOUT: 5m
      CGO_ENABLED: 1
    silent: true
  run-security-scan:
    cmds:
      - ./devops/security/run-dependency-check.sh
      - docker-compose -f devops/security/docker-compose.yml --profile tools run trivy
      - docker-compose -f devops/security/docker-compose.yml --profile tools run grype
      - docker-compose -f devops/security/docker-compose.yml --profile tools run snyk
    silent: false
  run-sonarqube-scan:
    cmds:
      - docker-compose -f devops/security/docker-compose.yml --profile tools run sonar-scanner
    silent: false
  docker-login:
    dotenv: ['{{.FRONTEND_DIR}}/.env']
    cmds:
      # - docker login ${REGISTRY_URL} -u ${REGISTRY_USER} -p {{.REGISTRY_PASSWORD}}
      - echo "{{.REGISTRY_PASSWORD}}" | docker login ${REGISTRY_URL} -u ${REGISTRY_USER} --password-stdin
    silent: true
    vars:
      REGISTRY_AWS_PROFILE: "${REGISTRY_AWS_PROFILE}"
      REGISTRY_AWS_REGION: "${REGISTRY_AWS_REGION}"
      REGISTRY_PASSWORD:
        sh: echo '$(aws ecr get-login-password --region {{.REGISTRY_AWS_REGION}} --profile {{.REGISTRY_AWS_PROFILE}})'
        # sh: echo "some-command-to-get-password-dynamically" 
        # For AWS: 
        # echo "$(aws ecr get-login-password --region us-east-1 --profile myprofile)"
        # For Google Cloud Platform (GCP):
        # echo "$(gcloud auth print-access-token)"
        # For Azure Container Registry (ACR):
        # echo "$(az acr login --name myregistry --expose-token --output tsv --query accessToken)"
  start-sonarqube-server:
    cmds:
    - docker-compose -f devops/security/docker-compose.yml --profile services up -d
  stop-sonarqube-server:
    cmds:
    - docker-compose -f devops/security/docker-compose.yml --profile services down
  docker-compose-exec-dev-frontend:
    cmds:
    - docker-compose -f devops/docker-compose.yaml exec dev-frontend sh
  docker-compose-build:
    cmds:
    - docker-compose -f devops/docker-compose.yaml build --no-cache
  docker-compose-up:
    cmds:
    - docker-compose -f devops/docker-compose.yaml up
    silent: true
  docker-build:
    dotenv: ['{{.FRONTEND_DIR}}/.env']
    cmds:
      # - docker build -t ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG} .
      - docker build -t ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG} --file devops/Dockerfile --target prod-frontend-ssr .
    silent: true
  docker-push:
    dotenv: ['{{.FRONTEND_DIR}}/.env']
    cmds:
      - docker push ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG}
    silent: true
  docker-publish:
    deps: [docker-login, docker-build]
    cmds:
      - task: docker-push
    silent: true


FROM golang:1.25-alpine AS base

# Add non-root user
RUN adduser -D -g '' appuser

# --------------------------------------------------------------------------------

# ---------- DEV ----------
FROM base AS dev

# Install Node.js and npm
RUN apk update && \
    apk add --no-cache nodejs npm curl

WORKDIR /workspace

COPY . .

RUN go install github.com/go-task/task/v3/cmd/task@latest

RUN task setup

# RUN go generate -tags=tools

# --------------------------------------------------------------------------------

# ---------- Dev BA<PERSON>KEND ----------
FROM dev AS dev-backend

ENV CGO_ENABLED=0
RUN go build -C services/backend/ -ldflags="-s -w"  -o backend cmd/main.go

WORKDIR /workspace/services/backend

CMD ["sh", "-c", "air -c .air.toml"]
# --------------------------------------------------------------------------------

# ---------- Dev FRONTEND ----------
FROM dev AS dev-frontend
ENV CGO_ENABLED=0

RUN task build


WORKDIR /workspace/services/frontend

CMD ["sh", "-c", "air -c .air.toml"]

# --------------------------------------------------------------------------------

# ---------- Prod BACKEND ----------
FROM base AS prod-backend
USER appuser
WORKDIR /app

# Copy project's binary and templates from /workspace to the scratch container.
COPY --from=dev-backend /workspace/services/backend/backend .


CMD ["sh", "-c", "./backend server start"]

# --------------------------------------------------------------------------------

# ---------- Prod FRONTEND WITH SSR ----------
FROM base AS prod-frontend-ssr

ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV APP_ENV=prod
ENV APP_SSR_ENABLED=true

RUN apk update && \
    apk add --no-cache nodejs npm curl

USER appuser
WORKDIR /workspace



COPY --chown=appuser --from=dev-frontend /workspace/services/frontend/go.mod .
COPY --chown=appuser --from=dev-frontend /workspace/services/frontend/go.sum .
# COPY --chown=appuser --from=dev-frontend /workspace/services/frontend/Procfile.prod ./Procfile
COPY --chown=appuser --from=dev-frontend /workspace/services/frontend/package.json .
COPY --chown=appuser --from=dev-frontend /workspace/services/frontend/package-lock.json .

RUN npm install 
# RUN go install cirello.io/runner/v3

COPY --from=dev-frontend /workspace/services/frontend/dist .
COPY --from=dev-frontend /workspace/libs /workspace/libs

CMD ["sh", "-c", "./frontend db init && ./frontend db migrate && ./frontend server start"]
# CMD ["sh", "-c", "runner -only \"npm-ssr web-prod\""]

# --------------------------------------------------------------------------------

# ---------- Prod FRONTEND ----------
FROM base AS prod-frontend

ENV APP_ENV=prod
ENV APP_SSR_ENABLED=false

USER appuser
WORKDIR /app

COPY --from=dev-frontend /workspace/services/frontend/dist .

CMD ["sh", "-c", "./frontend server start"]

# --------------------------------------------------------------------------------
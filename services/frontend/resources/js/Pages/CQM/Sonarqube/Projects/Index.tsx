import { usePage, router } from '@inertiajs/react';
import { ColumnProps } from '@/Types/ColumnProps';
import Table from '../../../../components/Table';
import React, { useState, useEffect } from 'react';
import Snackbar from '@/components/Snackbar';
import Breadcrumbs from '@/components/Breadcrumbs';
import SonarQubeProjectForm from '../../../../features/projects/components/SonarqubeProjectForm';
import { Project } from '@/Types/sonarProjects';
import { Pagination } from '@/Types/Pagination';

interface Data {
  sonarProjects: Project[];
  errors: string[];
}

interface EnvProp {
  jiraURL: string;
}

// Define the page props interface
interface PageProps {
  sonarProjects: Project[];
  sonarProjectsPagination: Pagination;
  errors: string[];
  env: EnvProp;
  [key: string]: any; // Add an index signature to satisfy the constraint
}

export default function Projects() {
  const page = usePage<PageProps>();
  const { errors, env, sonarProjects, sonarProjectsPagination } = page.props;
  const [sonarProject, setSonarProject] = useState<Project | null>(null);

  const queryParams = new URLSearchParams(page.url.split('?')[1]);
  const search = queryParams.get('name') || '';
  const pageParam = queryParams.get('page') || 1;
  const [currentPage, setCurrentPage] = useState<number>(pageParam);
  const [searchQuery, setSearchQuery] = useState<string>(search);
  const [isSonarModalOpen, setIsSonarModalOpen] = useState(false);

  // Initialize local state from props (sonarProjects and errors)
  const [projects, setProjects] = useState<Project[]>(sonarProjects);
  const [errorsState, setErrorsState] = useState(errors);
  const [jiraURL] = useState(env.jiraURL);

  // Detect changes in errors prop
  useEffect(() => {
    setErrorsState(errors);
  }, [errors]);

  // Function to set the sonar project for editing
  const editSonarProject = (project: Project) => {
    return () => {
      setSonarProject(project);
      setIsSonarModalOpen(true);
    };
  };

  // Define table columns
  const columns: Array<ColumnProps<Project>> = [
    { key: 'projectName', title: 'Sonar Project Name' },
    { key: 'jiraProject.name', title: 'Jira Project Name' },
    { key: 'Active', title: 'Active', render: (column, row) => (row.active ? 'Yes' : 'No') },
    { key: 'branch', title: 'Branch' },
    {
      key: 'actions',
      title: 'Actions',
      render: (column, row) => (
        <div className='flex gap-2'>
          <button onClick={editSonarProject(row)} className='btn btn-sm btn-primary'>
            Edit
          </button>
          <a href={`/cqm/sonarqube/projects/${row.id}/issues`} className='btn btn-sm btn-secondary'>
            SonarQube Issues
          </a>
        </div>
      ),
    },
  ];

  // Fetch projects from the server
  function fetchProjects(query: string, page: number) {
    router.get('/cqm/sonarqube/projects', { name: query, page: page }, { preserveState: true, replace: true });
  }

  // Search config for table search input
  const searchConfig = {
    searchQuery,
    placeholder: 'Search by project name',
    onSearchChange: (query: string) => {
      setCurrentPage(1);
      setSearchQuery(query);
      fetchProjects(query, 1);
    },
  };

  const pageChanged = (page: number) => {
    setCurrentPage(page);
    fetchProjects(searchQuery, page);
  };

  useEffect(() => {
    if (sonarProjects !== projects) {
      setProjects(sonarProjects);
    }
  }, [sonarProjects]); // Only update if sonarProjects changes

  const sonarProjectUpdated = (updatedSonarProject: Project) => {
    console.log('Sonar Project Updated', updatedSonarProject);
    setProjects((prevProjects) => prevProjects.map((project) => (project.id === updatedSonarProject.id ? updatedSonarProject : project)));
  };

  return (
    <div className='mx-auto p-4 container'>
      <Breadcrumbs items={[{ title: 'Home', href: '/' }, { title: 'SonarQube Projects' }]} />

      <div className='flex flex-col gap-2'>
        {errorsState.length > 0 && (
          <Snackbar message={errorsState[0]} type='error' autoDismiss={false} title='Error' uniqueKey={new Date().getTime()} />
        )}
        <button
          className='self-end btn btn-primary'
          onClick={() => {
            setIsSonarModalOpen(true);
          }}
        >
          Create Project
        </button>
        {isSonarModalOpen && (
          <SonarQubeProjectForm
            defaultURL={jiraURL}
            sonarProject={sonarProject}
            onClose={() => {
              setSonarProject(null);
              setIsSonarModalOpen(false);
            }}
            onUpdate={(sonarProject: Project) => sonarProjectUpdated(sonarProject)}
          />
        )}
        <Table
          data={projects}
          columns={columns}
          searchConfig={searchConfig}
          pagination={sonarProjectsPagination}
          onPageChange={(page: number) => pageChanged(page)}
          currentPage={currentPage}
        />
      </div>
    </div>
  );
}

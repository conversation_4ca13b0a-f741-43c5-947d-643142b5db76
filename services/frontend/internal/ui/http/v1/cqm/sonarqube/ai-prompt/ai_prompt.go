package aiprompt

import (
	"encoding/json"
	"log"
	"net/http"
	"sa-intranet/usecase/cqm/interactor"

	"app/frontend/internal/ui"

	"github.com/samber/do"
)

// RegisterRoutes registers the AI prompt generation routes
func RegisterRoutes(app *ui.App) {
	usecase := do.MustInvoke[*interactor.AIPromptInteractor](app.Injector())
	ctrl := &Controller{
		usecase: usecase,
		config:  app.AppConfig,
	}

	app.Router().POST("/cqm/sonarqube/projects/{projectID}/ai-prompt", nil, ctrl.GenerateAIPromptHandler())
}

// Controller handles AI prompt generation HTTP requests
type Controller struct {
	usecase *interactor.AIPromptInteractor
	config  *ui.AppConfig
}

// GenerateAIPromptHandler handles POST requests to generate AI prompts for SonarQube issues
func (c *Controller) GenerateAIPromptHandler() func(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		// Parse request body
		var req interactor.GenerateAIPromptRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			log.Printf("Failed to decode request body: %v", err)
			http.Error(w, "Invalid JSON request body", http.StatusBadRequest)
			return
		}

		// Get project ID from URL path
		projectID := r.PathValue("projectID")
		req.ProjectID = projectID

		// Generate AI prompt
		response, err := c.usecase.GeneratePrompt(r.Context(), req)
		if err != nil {
			c.handleError(w, err)
			return
		}

		// Return successful response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if err := json.NewEncoder(w).Encode(response); err != nil {
			log.Printf("Failed to encode response: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
			return
		}
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(fn)
	}
}

// handleError handles different types of errors and returns appropriate HTTP responses
func (c *Controller) handleError(w http.ResponseWriter, err error) {
	log.Printf("AI prompt generation error: %v", err)

	switch {
	case err == interactor.ErrAIProjectNotFound:
		http.Error(w, "Project not found", http.StatusNotFound)
	case err == interactor.ErrAIInvalidIssueKeys:
		http.Error(w, "Invalid issue keys provided", http.StatusBadRequest)
	case err == interactor.ErrAIIssuesNotFound:
		http.Error(w, "Some issues not found in SonarQube", http.StatusNotFound)
	case err == interactor.ErrAITooManyIssues:
		http.Error(w, "Too many issues requested, maximum 50 allowed", http.StatusBadRequest)
	case err == interactor.ErrAIPromptGeneration:
		http.Error(w, "Failed to generate AI prompt", http.StatusInternalServerError)
	default:
		// Check if it's a validation error
		if isValidationError(err) {
			http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
			return
		}
		// Generic internal server error
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// isValidationError checks if the error is a validation error
func isValidationError(err error) bool {
	// Check if error message contains validation-related keywords
	errMsg := err.Error()
	return contains(errMsg, "validation") || contains(errMsg, "required") || contains(errMsg, "invalid")
}

// contains checks if a string contains a substring (case-insensitive helper)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && containsHelper(s, substr)))
}

// containsHelper is a simple substring search helper
func containsHelper(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

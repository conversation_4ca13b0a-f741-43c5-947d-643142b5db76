package aiprompt_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"strings"
	"testing"

	aiprompt "app/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt"

	"app/frontend/internal/ui"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestGenerateAIPromptHandler(t *testing.T) {
	tests := []struct {
		name           string
		projectID      string
		requestBody    interface{}
		setupProject   bool
		expectedStatus int
		expectedError  string
		verify         func(t *testing.T, response *httptest.ResponseRecorder)
	}{
		{
			name:      "successful AI prompt generation",
			projectID: "", // Will be set in test
			requestBody: map[string]interface{}{
				"issueKeys": []string{"TEST-ISSUE-1"},
			},
			setupProject:   true,
			expectedStatus: http.StatusOK,
			verify: func(t *testing.T, response *httptest.ResponseRecorder) {
				var aiResponse interactor.AIPromptResponse
				if err := json.NewDecoder(response.Body).Decode(&aiResponse); err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}
				if aiResponse.IssueCount != 1 {
					t.Errorf("Expected 1 issue, got %d", aiResponse.IssueCount)
				}
				if aiResponse.Prompt == "" {
					t.Error("Expected non-empty prompt")
				}
				if !strings.Contains(aiResponse.Prompt, "SonarQube Issues AI Fix Prompt") {
					t.Error("Expected prompt to contain header")
				}
			},
		},
		{
			name:           "invalid JSON request body",
			projectID:      uuid.New().String(),
			requestBody:    "invalid-json",
			setupProject:   false,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid JSON request body",
			verify: func(t *testing.T, response *httptest.ResponseRecorder) {
				if !strings.Contains(response.Body.String(), "Invalid JSON") {
					t.Error("Expected invalid JSON error message")
				}
			},
		},
		{
			name:      "empty issue keys",
			projectID: "", // Will be set in test
			requestBody: map[string]interface{}{
				"issueKeys": []string{},
			},
			setupProject:   true,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Validation failed",
			verify: func(t *testing.T, response *httptest.ResponseRecorder) {
				if !strings.Contains(response.Body.String(), "Validation failed") {
					t.Error("Expected validation error message")
				}
			},
		},
		{
			name:      "project not found",
			projectID: "00000000-0000-0000-0000-000000000000",
			requestBody: map[string]interface{}{
				"issueKeys": []string{"TEST-ISSUE-1"},
			},
			setupProject:   false,
			expectedStatus: http.StatusNotFound,
			expectedError:  "Project not found",
			verify: func(t *testing.T, response *httptest.ResponseRecorder) {
				if !strings.Contains(response.Body.String(), "Project not found") {
					t.Error("Expected project not found error message")
				}
			},
		},
		{
			name:      "too many issues",
			projectID: "", // Will be set in test
			requestBody: map[string]interface{}{
				"issueKeys": make([]string, 51), // More than MaxIssuesPerRequest
			},
			setupProject:   true,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Too many issues requested",
			verify: func(t *testing.T, response *httptest.ResponseRecorder) {
				if !strings.Contains(response.Body.String(), "Too many issues requested") {
					t.Error("Expected too many issues error message")
				}
			},
		},
		{
			name:      "invalid project ID format",
			projectID: "invalid-uuid",
			requestBody: map[string]interface{}{
				"issueKeys": []string{"TEST-ISSUE-1"},
			},
			setupProject:   false,
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Validation failed",
			verify: func(t *testing.T, response *httptest.ResponseRecorder) {
				if !strings.Contains(response.Body.String(), "Validation failed") {
					t.Error("Expected validation error message")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test environment
			app := setupTestApp(t)

			var projectID string
			if tt.setupProject {
				projectID = createTestProject(t, app)
			} else {
				projectID = tt.projectID
			}

			// Create request body
			var requestBody []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				requestBody = []byte(str)
			} else {
				requestBody, err = json.Marshal(tt.requestBody)
				if err != nil {
					t.Fatalf("Failed to marshal request body: %v", err)
				}
			}

			// Create HTTP request
			req := httptest.NewRequest(http.MethodPost, "/cqm/sonarqube/projects/"+projectID+"/ai-prompt", bytes.NewReader(requestBody))
			req.Header.Set("Content-Type", "application/json")
			req.SetPathValue("projectID", projectID)

			// Create response recorder
			w := httptest.NewRecorder()

			// Get controller and execute handler
			ctrl := &aiprompt.Controller{
				// Note: We would need to expose these fields or create a test constructor
			}

			// Execute handler
			handler := ctrl.GenerateAIPromptHandler()
			handler(nil).ServeHTTP(w, req)

			// Verify status code
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Verify error message if expected
			if tt.expectedError != "" && !strings.Contains(w.Body.String(), tt.expectedError) {
				t.Errorf("Expected error message to contain '%s', got '%s'", tt.expectedError, w.Body.String())
			}

			// Run custom verification
			if tt.verify != nil {
				tt.verify(t, w)
			}
		})
	}
}

func TestGenerateAIPromptHandlerContentType(t *testing.T) {
	app := setupTestApp(t)
	projectID := createTestProject(t, app)

	requestBody := map[string]interface{}{
		"issueKeys": []string{"TEST-ISSUE-1"},
	}
	body, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPost, "/cqm/sonarqube/projects/"+projectID+"/ai-prompt", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	req.SetPathValue("projectID", projectID)

	w := httptest.NewRecorder()

	ctrl := &aiprompt.Controller{
		// Note: We would need to expose these fields or create a test constructor
	}

	handler := ctrl.GenerateAIPromptHandler()
	handler(nil).ServeHTTP(w, req)

	// Verify content type
	contentType := w.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type 'application/json', got '%s'", contentType)
	}
}

// setupTestApp creates a test application with all dependencies
func setupTestApp(t *testing.T) *ui.App {
	t.Helper()

	// This would need to be implemented based on your test setup patterns
	// For now, returning nil as placeholder
	return nil
}

// createTestProject creates a test project and returns its ID
func createTestProject(t *testing.T, app *ui.App) string {
	t.Helper()

	// Create test data similar to the interactor tests
	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](app.Injector())
	companyClient := &model.CompanyClient{
		Name: "Test Company for AI Prompt HTTP",
	}
	savedClient, err := companyClientRepo.Save(context.Background(), companyClient)
	if err != nil {
		t.Fatalf("Failed to create test company client: %v", err)
	}

	jiraProjectRepo := do.MustInvoke[repository.JiraProjectRepository](app.Injector())
	jiraProject := &model.JiraProject{
		ProjectKey:      "AIHTTPTEST",
		Name:            "AI HTTP Test Jira Project",
		JiraURL:         "https://aihttptest.atlassian.net",
		Username:        "<EMAIL>",
		Active:          true,
		Token:           "encrypted-token",
		CompanyClientID: savedClient.ID,
	}
	savedJiraProject, err := jiraProjectRepo.Save(context.Background(), jiraProject)
	if err != nil {
		t.Fatalf("Failed to create test Jira project: %v", err)
	}

	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](app.Injector())
	sonarProject := &model.SonarqubeProject{
		ProjectKey:    "ai-http-test-sonar-project",
		ProjectName:   "AI HTTP Test SonarQube Project",
		Branch:        "main",
		JiraProjectID: savedJiraProject.ID,
		Active:        true,
	}
	savedSonarProject, err := sonarProjectRepo.Save(context.Background(), sonarProject, true)
	if err != nil {
		t.Fatalf("Failed to create test SonarQube project: %v", err)
	}

	return savedSonarProject.ID.String()
}

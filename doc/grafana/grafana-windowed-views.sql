WITH windowed AS (
  SELECT *
  FROM vw_sonarqube_projects_with_analysis
  WHERE analysed_at <= $__timeTo()
),
ranked AS (
  SELECT
    w.*,
    ROW_NUMBER() OVER (
      PARTITION BY w.sonarqube_project_id
      ORDER BY w.analysed_at DESC
    ) AS rn
  FROM windowed w
)
SELECT 
    spl.company_client_name AS "Client",
    spl.jira_project_name AS "JIRA Project",
    spl.sonar_project_name AS "SonarQube Project",
    TO_CHAR(spl.analysed_at, 'YYYY-MM-DD') AS "Last Analysis",
    spl.score AS "Code Quality Score",
    spl.measure_open_issues AS "Open Issues",
    spl.measure_code_smells AS "Code Smells",
        spl.measure_bugs AS "Total Bugs",
    spl.measure_total_vulnerabilities AS "Total Vulnerabilities",
    spl.measure_blocker_violations AS "Blocker Vulnerabilities",
    spl.measure_critical_violations AS "Critical Vulnerabilities",
    spl.measure_major_violations AS "Major Vulnerabilities",
    spl.measure_coverage AS "Overall Code Coverage (%)",
    spl.measure_new_coverage  AS "New Code Coverage (%)",
    spl.measure_security_rating_grade AS "Security Rating",
    spl.measure_security_rating AS "Security Rating Number",
    spl.measure_sqale_rating_grade AS "Maintainability Rating",
    spl.measure_sqale_rating AS "Maintainability Rating Number",
    spl.measure_reliability_rating_grade AS "Reliability Rating",
    spl.measure_reliability_rating AS "Reliability Rating Number"
FROM ranked spl
WHERE rn = 1
ORDER BY analysed_at;


-- INSTEAD OF

WITH cte_vw_latest_sonarqube_analyses AS(
SELECT
        sa.*,
        ROW_NUMBER() OVER (PARTITION BY sonarqube_project_id ORDER BY analysed_at DESC) AS rn
    FROM
        vw_sonarqube_analyses AS sa
    INNER JOIN sonarqube_projects sp ON sa.sonarqube_project_id = sp.id
    INNER JOIN jira_projects jp ON sp.jira_project_id = jp.id
    WHERE
        status = 'succeeded' AND score IS NOT NULL AND sp.active = true AND jp.active = true AND analysed_at <= $__timeTo() 
), cte_vw_sonarqube_projects_with_latest_analysis AS (
    SELECT 
    sp.id,
    sp.jira_project_id,
    sp.project_name AS sonar_project_name,
    sp.project_key AS sonar_project_key,
    lsa.id AS sonarqube_analysis_id,
    lsa.quality_gate_name AS quality_gate_name,
    TRIM(lsa.quality_gate_status) AS quality_gate_status,
    TRIM(lsa.status) AS status,
    lsa.analysed_at,
    lsa.score AS score,
    lsa.measure_open_issues AS measure_open_issues,
    lsa.measure_coverage AS measure_coverage,
    lsa.measure_new_coverage AS measure_new_coverage,
    lsa.measure_reliability_rating AS measure_reliability_rating,
    lsa.measure_new_reliability_rating AS measure_new_reliability_rating,
    lsa.measure_security_review_rating AS measure_security_review_rating,
    lsa.measure_new_security_review_rating AS measure_new_security_review_rating,
    lsa.measure_security_rating AS measure_security_rating,
    lsa.measure_new_security_rating AS measure_new_security_rating,
    lsa.measure_sqale_rating AS measure_sqale_rating,
    lsa.measure_new_sqale_rating AS measure_new_sqale_rating,
    lsa.measure_duplicated_lines AS measure_duplicated_lines,
    lsa.measure_duplicated_lines_density AS measure_duplicated_lines_density,
    lsa.measure_code_smells AS measure_code_smells,
    lsa.measure_vulnerable_dependencies AS measure_vulnerable_dependencies,
    lsa.measure_total_vulnerabilities AS measure_total_vulnerabilities,
    lsa.measure_vulnerabilities AS measure_vulnerabilities,
    lsa.measure_medium_severity_vulns AS measure_medium_severity_vulns,
    lsa.measure_low_severity_vulns AS measure_low_severity_vulns,
    lsa.measure_high_severity_vulns AS measure_high_severity_vulns,
    lsa.measure_violations AS measure_violations,
    lsa.measure_minor_violations AS measure_minor_violations,
    lsa.measure_major_violations AS measure_major_violations,
    lsa.measure_critical_violations AS measure_critical_violations,
    lsa.measure_blocker_violations AS measure_blocker_violations,
    lsa.measure_bugs AS measure_bugs,
    COALESCE(lsa.measure_new_bugs, 0) AS measure_new_bugs,
    lsa.measure_security_hotspots AS measure_security_hotspots,
    lsa.measure_lines AS measure_lines,
    number_to_grade(lsa.measure_reliability_rating) AS measure_reliability_rating_grade,
    number_to_grade(lsa.measure_security_rating) AS measure_security_rating_grade,
    number_to_grade(lsa.measure_security_review_rating) AS measure_security_review_rating_grade,
    number_to_grade(lsa.measure_sqale_rating) AS measure_sqale_rating_grade,
    number_to_grade(lsa.measure_new_reliability_rating) AS measure_new_reliability_rating_grade,
    number_to_grade(lsa.measure_new_security_rating) AS measure_new_security_rating_grade,
    number_to_grade(lsa.measure_new_security_review_rating) AS measure_new_security_review_rating_grade,
    number_to_grade(lsa.measure_new_sqale_rating) AS measure_new_sqale_rating_grade,
    sp.branch AS sonarqube_branch,
    jp.name AS jira_project_name,
    jp.project_key AS jira_project_key,
    jp.jira_url AS jira_url,
    cc.name AS company_client_name,
    cc.id AS company_client_id
FROM sonarqube_projects sp
INNER JOIN jira_projects jp ON sp.jira_project_id = jp.id
INNER JOIN company_clients cc ON jp.company_client_id = cc.id
INNER JOIN cte_vw_latest_sonarqube_analyses lsa ON sp.id = lsa.sonarqube_project_id AND lsa.rn = 1
WHERE sp.active = true AND jp.active = true 
)


SELECT 
    spl.company_client_name AS "Client",
    spl.jira_project_name AS "JIRA Project",
    spl.sonar_project_name AS "SonarQube Project",
    TO_CHAR(spl.analysed_at, 'YYYY-MM-DD') AS "Last Analysis",
    spl.score AS "Code Quality Score",
    spl.measure_open_issues AS "Open Issues",
    spl.measure_code_smells AS "Code Smells",
        spl.measure_bugs AS "Total Bugs",
    spl.measure_total_vulnerabilities AS "Total Vulnerabilities",
    spl.measure_blocker_violations AS "Blocker Vulnerabilities",
    spl.measure_critical_violations AS "Critical Vulnerabilities",
    spl.measure_major_violations AS "Major Vulnerabilities",
    spl.measure_coverage AS "Overall Code Coverage (%)",
    spl.measure_new_coverage  AS "New Code Coverage (%)",
    spl.measure_security_rating_grade AS "Security Rating",
    spl.measure_security_rating AS "Security Rating Number",
    spl.measure_sqale_rating_grade AS "Maintainability Rating",
    spl.measure_sqale_rating AS "Maintainability Rating Number",
    spl.measure_reliability_rating_grade AS "Reliability Rating",
    spl.measure_reliability_rating AS "Reliability Rating Number"
    -- spl.id AS sonar_project_id,
    -- spl.jira_project_id,
    -- spl.company_client_id
FROM cte_vw_sonarqube_projects_with_latest_analysis spl
-- WHERE analysed_at <= $__timeTo() 
ORDER BY analysed_at;
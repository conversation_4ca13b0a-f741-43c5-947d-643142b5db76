{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 79, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "orange", "value": 40}, {"color": "light-orange", "value": 70}, {"color": "#EAB839", "value": 80}, {"color": "green", "value": 90}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 0, "y": 0}, "id": 13, "options": {"afterRender": "", "content": "{{#with data.[0]}}\n<div class=\"text-center\" style=\"color: {{statusColor}};\">\n  <div class=\"mb-0 text-[4em]\">\n    {{overall_average_score}} \n  </div>\n  <div class=\"text-[2em]\">\n    ({{overall_average_score_name}})\n  </div>\n</div>\n{{/with}}", "contentPartials": [], "defaultContent": "The query didn't return any results.", "editor": {"format": "auto", "language": "markdown"}, "editors": [], "externalStyles": [], "helpers": "", "renderMode": "allRows", "status": "overall_average_score", "styles": "", "wrap": true}, "pluginVersion": "5.7.0", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\r\n    -- Average score from latest analysis for active SonarQube projects\r\n    ROUND(AVG(spl.score), 2) AS overall_average_score,\r\n    classify_score(ROUND(AVG(spl.score), 2)) AS overall_average_score_name\r\n\r\nFROM vw_sonarqube_projects_with_latest_analysis spl\r\n\r\n\r\n-- SELECT\r\n--     (SELECT AVG(average_sonarproject_score)::numeric(5,2) FROM vw_company_summary ) AS overall_average_score,\r\n--     classify_score((SELECT AVG(average_sonarproject_score)::numeric(5,2) FROM vw_company_summary )) AS overall_average_score_name\r\n-- FROM\r\n--     vw_company_summary cs\r\n-- ORDER BY\r\n--     cs.company_client_name;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Overall Score (Target: 100)", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>-dynamictext-panel"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 2, "x": 3, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(cc.id) \r\nFROM company_clients as cc \r\nINNER JOIN jira_projects jp ON jp.company_client_id = cc.id\r\nWHERE jp.active = true;\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total Clients", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 2, "x": 5, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(jp.id) \r\nFROM jira_projects as jp \r\nINNER JOIN company_clients cc ON jp.company_client_id = cc.id\r\nWHERE jp.active = true;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total JIRA projects", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 7, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT count(sp.id) \r\nFROM sonarqube_projects sp\r\nINNER JOIN jira_projects jp ON sp.jira_project_id = jp.id\r\nINNER JOIN company_clients cc ON jp.company_client_id = cc.id\r\nWHERE sp.active = true AND jp.active = true;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total SonarQube projects (repositories)", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 10, "y": 0}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\r\n    COUNT(spl.id)\r\nFROM\r\n    vw_sonarqube_projects_with_latest_analysis AS spl\r\nWHERE LOWER(spl.quality_gate_status) = 'ok';\r\n\r\n\r\n\r\n\r\n\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total SonarQube projects passing quality gate", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "description": "Projects below \"Fair\" rating", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 2, "x": 14, "y": 0}, "id": 7, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT COUNT(*) FROM vw_sonarqube_projects_with_latest_analysis AS spl WHERE spl.score < score_min_boundary('fair')\r\n\r\n\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total critical projects", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "description": "Projects below \"Good\" rating", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 16, "y": 0}, "id": 15, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT COUNT(*) FROM vw_sonarqube_projects_with_latest_analysis AS spl WHERE spl.score < score_min_boundary('good')\r\n\r\n\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total projects below expectation", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 0}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT SUM(spl.measure_open_issues) FROM vw_sonarqube_projects_with_latest_analysis spl;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Total opened issues overall", "type": "stat"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "links": [{"title": "View Sonar Projects", "url": "/d/${__dashboard.uid}/${__dashboard}?var-company_client_id=${__data.fields.company_client_id}&var-jira_project_id=${__data.fields.id}"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "id"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "company_client_id"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "displayName", "value": "Project name"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "company_client_name"}, "properties": [{"id": "displayName", "value": "Client name"}]}]}, "gridPos": {"h": 16, "w": 6, "x": 0, "y": 5}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \r\n  cc.name AS company_client_name,\r\n  jp.id AS id,\r\n  jp.name,\r\n  jp.company_client_id\r\n  FROM jira_projects jp\r\n  INNER JOIN company_clients cc ON cc.id = jp.company_client_id\r\n  ORDER BY cc.name;\r\n\r\n\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "JIRA projects", "type": "table"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "description": "", "fieldConfig": {"defaults": {"thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 26, "w": 18, "x": 6, "y": 5}, "id": 12, "options": {"afterRender": "function loadTailwindFromCDN() {\r\n  var responseData = \"\";\r\n\r\n  const script = document.createElement(\"script\");\r\n\r\n  script.src = \"https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4\";\r\n  document.body.appendChild(script);\r\n\r\n  console.log(\"script\" + script);\r\n}\r\n\r\nloadTailwindFromCDN()", "content": "\n<h1 class=\"text-3xl font-bold mb-4\">\n  {{#with data.[0].[0]}}\n  Client: {{company_client_name}}\n  {{/with}}\n  {{#with data.[2].[0]}}\n    - ({{score}}%)\n  {{/with}}\n</h1>\n\n<h2 class=\"text-2xl font-semibold  mb-4\">\n  JIRA project:\n  <a\n    class=\"hover:underline\"\n    style=\"color: var(--link-color)\"\n    target=\"_blank\"\n    {{#with data.[0].[0]}}\n    href=\"{{jira_url}}/jira/software/c/projects/{{jira_project_key}}/summary\"\n    {{/with}}\n  >\n    {{#with data.[0].[0]}}\n    {{jira_project_name}}\n    {{/with}}\n    {{#with data.[1].[0]}}\n    - ({{score}}%)\n    {{/with}}\n  </a>\n</h2>\n\n<hr class=\"h-px my-8 bg-gray-200 border-0 dark:bg-gray-700\">\n\n\n{{#each data.[0]}}\n<div class=\"shadow-lg rounded-lg mb-6 border-l-8 {{#if (eq quality_gate_status \"OK\")}}border-green-500{{else}}border-red-500{{/if}}\">\n  <div class=\"p-4\">\n    <div class=\"flex justify-between items-center mb-4\">\n      <h2 class=\"text-xl font-bold\">\n        <a style=\"color: var(--link-color)\" class=\" hover:underline\" target=\"_blank\" href=\"https://sonarqube.applaudostudios.com/dashboard?id={{sonar_project_key}}&branch={{sonarqube_branch}}\">\n          {{sonar_project_name}} - {{score}}% ({{score_grade}})\n        </a>\n      </h2>\n      <span class=\"text-sm font-semibold px-3 py-1 rounded-full {{#if (eq quality_gate_status \"OK\")}}bg-green-100 text-green-800{{else}}bg-red-100 text-red-800{{/if}}\">\n        {{#if (eq quality_gate_status \"OK\")}}PASSED{{else}}FAILED{{/if}}\n      </span>\n    </div>\n\n    <div class=\"grid grid-cols-6 gap-4 text-sm\">\n      <div>\n        <p class=\"text-gray-500 font-medium\">RELIABILITY</p>\n        <div class=\"grid grid-cols-2 gap-4 text-sm font-medium text-gray-700\">\n          <div class=\"font-semibold\">Overall</div>\n          <div class=\"font-semibold\">New</div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_reliability_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_reliability_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_reliability_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_reliability_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_reliability_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_new_reliability_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_new_reliability_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_new_reliability_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_new_reliability_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_new_reliability_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n        </div>\n        <p class=\"text-lg font-bold\">{{measure_bugs}} Bugs</p>\n        <p class=\"text-lg font-bold\">{{measure_new_bugs}} New Bugs</p>\n      </div>\n\n      <div>\n        <p class=\"text-gray-500 font-medium\">SECURITY</p>\n        <div class=\"grid grid-cols-2 gap-4 text-sm font-medium text-gray-700\">\n          <div class=\"font-semibold\">Overall</div>\n          <div class=\"font-semibold\">New</div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_security_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_security_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_security_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_security_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_security_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_new_security_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_new_security_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_new_security_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_new_security_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_new_security_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n        </div>\n        <p class=\"text-lg text-gray-600 font-bold\">{{measure_open_issues}} Open Issues</p>\n        <p class=\"text-lg font-bold\">{{measure_total_vulnerabilities}} Vulnerabilities</p>\n        <p class=\"text-md font-bold\">{{measure_blocker_violations}} Blocker Vulnerabilities</p>\n        <p class=\"text-md font-bold\">{{measure_critical_violations}} Critical Vulnerabilities</p>\n        <p class=\"text-md font-bold\">{{measure_major_violations}} Major Vulnerabilities</p>\n      </div>\n\n      <div>\n        <p class=\"text-gray-500 font-medium\">MAINTAINABILITY</p>\n        <div class=\"grid grid-cols-2 gap-4 text-sm font-medium text-gray-700\">\n          <div class=\"font-semibold\">Overall</div>\n          <div class=\"font-semibold\">New</div>\n          \n          <div class=\"text-lg\">\n            {{#if (eq measure_sqale_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_sqale_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_sqale_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_sqale_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_sqale_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_new_sqale_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_new_sqale_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_new_sqale_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_new_sqale_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_new_sqale_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n        </div>\n        <p class=\"text-lg font-bold\">{{measure_code_smells}} Code smells</p>\n\n      </div>\n\n      <div>\n        <p class=\"text-gray-500 font-medium\">SECURITY REVIEW</p>\n        <div class=\"grid grid-cols-2 gap-4 text-sm font-medium text-gray-700\">\n          <div class=\"font-semibold\">Overall</div>\n          <div class=\"font-semibold\">New</div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_security_review_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_security_review_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_security_review_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_security_review_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_security_review_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n\n          <div class=\"text-lg\">\n            {{#if (eq measure_new_security_review_rating_grade \"A\")}}\n              <span class=\"text-green-600\">A</span>\n            {{else if (eq measure_new_security_review_rating_grade \"B\")}}\n              <span class=\"text-lime-600\">B</span>\n            {{else if (eq measure_new_security_review_rating_grade \"C\")}}\n              <span class=\"text-yellow-500\">C</span>\n            {{else if (eq measure_new_security_review_rating_grade \"D\")}}\n              <span class=\"text-orange-500\">D</span>\n            {{else if (eq measure_new_security_review_rating_grade \"E\")}}\n              <span class=\"text-red-600\">E</span>\n            {{/if}}\n          </div>\n        </div>\n        <p class=\"text-lg font-bold\">{{measure_security_hotspots}} Security Hot Spots</p>\n        \n\n      </div>\n\n      <div>\n        <p class=\"text-gray-500 font-medium\">COVERAGE</p>\n        <p class=\"text-lg font-bold\">{{measure_coverage}}%</p>\n      </div>\n\n      <div>\n        <p class=\"text-gray-500 font-medium\">DUPLICATION</p>\n        <p class=\"text-lg font-bold\">{{measure_duplicated_lines_density}}% Line Density</p>\n      </div>\n    </div>\n    <div class=\"grid grid-cols-5 gap-4 text-sm\">\n      <div class=\"mt-4 text-xs text-gray-600\">\n        <p>Quality Gate: {{quality_gate_name}}</p>\n      </div>\n      <div class=\"mt-4 text-xs text-gray-600\">\n        <p>Last analysis: {{localTime analysed_at_utc}}</p>\n      </div>\n      <div class=\"mt-4 text-xs text-gray-600\">\n        <p>ID: {{sonarqube_analysis_id}}</p>\n      </div>\n    </div>\n    \n  </div>\n</div>\n{{/each}}", "contentPartials": [], "defaultContent": "The query didn't return any results.", "editor": {"format": "auto", "language": "handlebars"}, "editors": ["default", "styles", "helpers", "afterRender"], "externalStyles": [], "helpers": "context.handlebars.registerHelper('localTime', function (utcString) {\r\n  return new Date(utcString).toLocaleString();\r\n});", "renderMode": "data", "styles": "", "wrap": false}, "pluginVersion": "5.7.0", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT spl.sonar_project_name,\r\nspl.sonar_project_key,\r\nspl.quality_gate_name,\r\nspl.quality_gate_status,\r\nspl.measure_bugs,\r\nspl.measure_new_bugs,\r\nspl.measure_reliability_rating,\r\nspl.measure_reliability_rating_grade,\r\nspl.measure_new_reliability_rating_grade,\r\nspl.measure_vulnerabilities,\r\nspl.measure_total_vulnerabilities,\r\nspl.measure_code_smells,\r\nspl.measure_security_hotspots,\r\nspl.measure_security_rating,\r\nspl.measure_security_rating_grade,\r\nspl.measure_new_security_rating,\r\nspl.measure_new_security_rating_grade,\r\nspl.measure_security_review_rating,\r\nspl.measure_security_review_rating_grade,\r\nspl.measure_sqale_rating,\r\nspl.measure_sqale_rating_grade,\r\nspl.measure_new_sqale_rating,\r\nspl.measure_new_sqale_rating_grade,\r\nspl.measure_coverage,\r\nspl.measure_duplicated_lines_density,\r\nspl.company_client_name,\r\nspl.jira_project_name,\r\nspl.jira_project_key,\r\nclassify_score(spl.score) AS score_grade,\r\nspl.score,\r\nspl.measure_open_issues,\r\nspl.measure_minor_violations,\r\nspl.measure_major_violations,\r\nspl.measure_critical_violations,\r\nspl.measure_blocker_violations,\r\nTO_CHAR(spl.analysed_at, 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') AS analysed_at_utc,\r\nspl.jira_url,\r\nspl.analysed_at,\r\nspl.sonarqube_branch,\r\nspl.sonarqube_analysis_id\r\nFROM vw_sonarqube_projects_with_latest_analysis spl\r\nWHERE jira_project_id = COALESCE(NULLIF('$jira_project_id', '')::uuid, '00000000-0000-0000-0000-000000000000'::uuid)\r\nORDER BY analysed_at;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT AVG(spl.score) as score\r\nFROM vw_sonarqube_projects_with_latest_analysis spl\r\nWHERE jira_project_id = COALESCE(NULLIF('$jira_project_id', '')::uuid, '00000000-0000-0000-0000-000000000000'::uuid);", "refId": "B", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "SELECT AVG(spl.score) as score\r\nFROM vw_sonarqube_projects_with_latest_analysis spl\r\nWHERE company_client_id = COALESCE(NULLIF('$company_client_id', '')::uuid, '00000000-0000-0000-0000-000000000000'::uuid)\r\nGROUP BY company_client_id;", "refId": "C", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "SonarQube Projects", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>-dynamictext-panel"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 6, "x": 0, "y": 21}, "id": 14, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\r\n    spl.jira_project_name,\r\n    spl.sonar_project_name\r\nFROM\r\n    vw_sonarqube_projects_with_latest_analysis AS spl\r\nWHERE LOWER(spl.quality_gate_status) = 'ok'\r\nORDER BY spl.jira_project_name DESC;\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Projects that passed quality gate", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "AMP - Smart Suite", "value": "f9c17ccf-6969-4474-b929-81b918a1ebbb"}, "datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "definition": "SELECT  name AS __text, id AS __value FROM jira_projects ORDER BY name ASC;", "hide": 2, "includeAll": false, "label": "Jira Project ID", "multi": false, "name": "jira_project_id", "options": [], "query": "SELECT  name AS __text, id AS __value FROM jira_projects ORDER BY name ASC;", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "AMP", "value": "d2b67bee-155f-4aa3-b992-3fcb1b759523"}, "datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "definition": "SELECT  name AS __text, id AS __value FROM company_clients ORDER BY name ASC;", "hide": 2, "includeAll": false, "label": "Company Client ID", "multi": false, "name": "company_client_id", "options": [], "query": "SELECT  name AS __text, id AS __value FROM company_clients ORDER BY name ASC;", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "cqm-overview-prod", "uid": "d96da101-d965-4cd1-8ae0-40d90a37353p", "version": 29, "weekStart": ""}
{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 105, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"afterRender": "", "content": "<h1 class=\"text-3xl font-bold mb-4\">\n  {{#with data.[0].[0]}}\n  Client: <strong>{{company_client_name}}</strong>\n  {{/with}}\n</h1>\n\n\n<h2 class=\"text-2xl font-semibold  mb-4\">\n  JIRA project:\n  <a\n    class=\"hover:underline\"\n    style=\"color: var(--link-color)\"\n    target=\"_blank\"\n    {{#with data.[0].[0]}}\n    href=\"{{jira_url}}/jira/software/c/projects/{{jira_project_key}}/summary\"\n    {{/with}}\n  >\n    <strong>\n    {{#with data.[0].[0]}}\n      {{jira_project_name}}\n    {{/with}}\n    {{#with data.[0].[0]}}\n    - ({{score}}%)\n    {{/with}}\n    </strong>\n  </a>\n</h2>\n\n\n<h3 class=\"text-2xl font-semibold  mb-4\">\n  Sonar project:\n  <a\n    class=\"hover:underline\"\n    style=\"color: var(--link-color)\"\n    target=\"_blank\"\n    {{#with data.[0].[0]}}\n    href=\"https://sonarqube.applaudostudios.com/dashboard?id={{sonar_project_key}}&branch={{sonarqube_branch}}\"\n    {{/with}}\n  >\n    <strong>\n    {{#with data.[0].[0]}}\n    {{sonar_project_name}}\n    {{/with}}\n    </strong>\n  </a>\n</h3>", "contentPartials": [], "defaultContent": "The query didn't return any results.", "editor": {"format": "auto", "language": "markdown"}, "editors": [], "externalStyles": [], "helpers": "", "renderMode": "data", "styles": "", "wrap": true}, "pluginVersion": "5.7.0", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT AVG(spl.score) as score,\r\n     spl.company_client_name,\r\n     spl.jira_project_name,\r\n     spl.sonar_project_name,\r\n     spl.sonar_project_key,\r\n     spl.sonarqube_branch,\r\n     spl.jira_project_key\r\nFROM vw_sonarqube_projects_with_latest_analysis spl\r\nWHERE id = COALESCE(NULLIF('$sonar_project_id', '')::uuid, '00000000-0000-0000-0000-000000000000'::uuid)\r\nGROUP BY spl.company_client_name,\r\n     spl.jira_project_name,\r\n     sonar_project_name,\r\n     spl.sonarqube_branch,\r\n     spl.sonar_project_key,\r\n     spl.jira_project_key\r\n;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>-dynamictext-panel"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "links": [{"title": "", "url": "/d/${__dashboard.uid}/${__dashboard}?var-jira_project_id=${__data.fields.jira_project_id}&var-sonar_project_id=${__data.fields.sonar_project_id}"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 4, "x": 0, "y": 4}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT spl.sonar_project_name AS \"SonarQube Project\",\r\n    TO_CHAR(spl.analysed_at, 'YYYY-MM-DD') AS \"Last Analysis\",\r\n    spl.score AS \"Code Quality Score\",\r\n    spl.measure_open_issues AS \"Open Issues\",\r\n    spl.measure_security_rating_grade AS \"Security Rating\",\r\n    spl.measure_total_vulnerabilities AS \"Total Vulnerabilities\",\r\n    spl.measure_blocker_violations AS \"Blocker Vulnerabilities\",\r\n    spl.measure_critical_violations AS \"Critical Vulnerabilities\",\r\n    spl.measure_major_violations AS \"Major Vulnerabilities\",\r\n    spl.measure_coverage AS \"Overall Code Coverage (%)\",\r\n    spl.measure_new_coverage  AS \"New Code Coverage (%)\",\r\n    spl.measure_sqale_rating_grade AS \"Maintainability Rating\",\r\n    spl.measure_code_smells AS \"Code Smells\",\r\n    spl.measure_reliability_rating_grade AS \"Reliability Rating\",\r\n    spl.measure_bugs AS \"Total Bugs\",\r\n    spl.id AS sonar_project_id,\r\n    spl.jira_project_id\r\nFROM vw_sonarqube_projects_with_latest_analysis spl\r\nWHERE jira_project_id = COALESCE(NULLIF('$jira_project_id', '')::uuid, '00000000-0000-0000-0000-000000000000'::uuid)\r\nORDER BY analysed_at;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "SonarQube Projects", "type": "table"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "gridPos": {"h": 11, "w": 20, "x": 4, "y": 4}, "id": 3, "options": {"baidu": {"callback": "bmapReady", "key": ""}, "editor": {"format": "auto", "height": 600}, "gaode": {"key": "", "plugin": "AMap.Scale,AMap.ToolBar"}, "getOption": "// ---- colors you want per metric ----\nconst colorMap = {\n  coverage: '#2E7D32',      // green\n  bugs: '#D32F2F',          // red\n  total_issues: '#F57C00',  // orange\n};\n\n// Put coverage on right axis? (counts left, % right)\nconst USE_DUAL_AXIS = true;\n\n// Stack counts (bugs + total_issues)? coverage stays unstacked\nconst STACK_COUNTS = false;\n\n// --- helper to get array from Grafana vector ---\nfunction arr(v) { return v?.toArray ? v.toArray() : (v?.buffer ?? v); }\n\n// Build series from the incoming frames\nlet series = [];\n\n// Case A: Grafana already sent one frame per series (nice & easy)\nif (data.series.length > 1 && data.series.every(f => f.fields.some(ff => ff.type === 'number'))) {\n  series = data.series.map((s) => {\n    const numField = s.fields.find((f) => f.type === 'number');\n    const timeField = s.fields.find((f) => f.type === 'time');\n\n    const values = arr(numField.values);\n    const times = arr(timeField.values);\n\n    const name = String(s.name || numField.name || 'value').trim();\n    const isCoverage = /coverage/i.test(name);\n    const color =\n      colorMap[name] ||\n      (isCoverage ? colorMap.coverage :\n        /bugs?/i.test(name) ? colorMap.bugs : colorMap.total_issues);\n\n    return {\n      name,\n      type: 'line',\n      color,\n      data: values.map((v, i) => [times[i], v]),\n      showSymbol: true,\n      symbol: 'circle',\n      symbolSize: 5,\n      lineStyle: { width: 2 },\n      yAxisIndex: (USE_DUAL_AXIS && isCoverage) ? 1 : 0,\n      stack: (STACK_COUNTS && !isCoverage) ? 'counts' : undefined,\n      emphasis: { focus: 'series' },\n    };\n  });\n} else if (data.series.length >= 1) {\n  // Case B: Grafana sent a single table-like frame with columns time/metric/value\n  const frame = data.series[0];\n  const timeF = frame.fields.find(f => f.type === 'time');\n  const metricF = frame.fields.find(f => f.type === 'string'); // expect name = 'metric'\n  const valueF = frame.fields.find(f => f.type === 'number');\n\n  const T = arr(timeF.values);\n  const M = arr(metricF.values);\n  const V = arr(valueF.values);\n\n  // Group rows by metric\n  const byMetric = new Map();\n  for (let i = 0; i < V.length; i++) {\n    if (T[i] == null || V[i] == null || M[i] == null) continue;\n    const key = String(M[i]).trim();\n    if (!byMetric.has(key)) byMetric.set(key, []);\n    byMetric.get(key).push([T[i], V[i]]);\n  }\n\n  // Build one ECharts series per metric\n  series = Array.from(byMetric.entries()).map(([name, points]) => {\n    const isCoverage = /coverage/i.test(name);\n    const color =\n      colorMap[name] ||\n      (isCoverage ? colorMap.coverage :\n        /bugs?/i.test(name) ? colorMap.bugs : colorMap.total_issues);\n\n    return {\n      name,\n      type: 'line',\n      color,\n      data: points,\n      showSymbol: true,\n      symbol: 'circle',\n      symbolSize: 5,\n      lineStyle: { width: 2 },\n      yAxisIndex: (USE_DUAL_AXIS && isCoverage) ? 1 : 0,\n      stack: (STACK_COUNTS && !isCoverage) ? 'counts' : undefined,\n      emphasis: { focus: 'series' },\n    };\n  });\n}\n\noption = {\n  title: { text: 'SonarQube Project Trend (last 3 months)' },\n  tooltip: { trigger: 'axis' },\n  legend: { type: 'plain', data: series.map(s => s.name) },\n  grid: { left: '3%', right: '4%', bottom: '8%', containLabel: true },\n  toolbox: { feature: { saveAsImage: {} } },\n  xAxis: { type: 'time', boundaryGap: false },\n  yAxis: USE_DUAL_AXIS\n    ? [\n      { type: 'value', name: 'Count (bugs/issues)', minInterval: 1 },\n      { type: 'value', name: 'Coverage (%)', min: 0, max: 100 }\n    ]\n    : { type: 'value' },\n  series,\n};\n\nreturn option;\n", "google": {"callback": "gmapReady", "key": ""}, "map": "none", "renderer": "canvas", "themeEditor": {"config": "{}", "height": 400, "name": "default"}}, "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "/* Time-series for coverage, total issues, and bugs\r\n   for a single SonarQube project over the last 3 months. */\r\n\r\nWITH s AS (\r\n  SELECT\r\n    sp.id                         AS sonarqube_project_id,\r\n    sp.project_name,\r\n    sp.project_key,\r\n    sp.branch,\r\n    sa.analysed_at,\r\n    sa.measure_coverage,          -- % coverage\r\n    sa.measure_bugs,              -- total bugs\r\n    sa.measure_open_issues AS issues_total,               -- total issues\r\n    sa.status\r\n  FROM vw_sonarqube_analyses sa\r\n  JOIN sonarqube_projects sp ON sp.id = sa.sonarqube_project_id\r\n  JOIN jira_projects jp       ON jp.id = sp.jira_project_id\r\n  WHERE sa.status = 'succeeded'\r\n    AND sp.active = TRUE\r\n    AND jp.active = TRUE\r\n    AND sa.analysed_at >= NOW() - INTERVAL '3 months'\r\n    AND sp.id = '$sonar_project_id'  -- 👈 replace with the actual UUID\r\n)\r\nSELECT\r\n  date_trunc('day', analysed_at)         AS \"time\",\r\n  metric,\r\n  value::numeric                         AS value\r\nFROM s\r\nCROSS JOIN LATERAL (\r\n  VALUES\r\n    ('coverage',      measure_coverage),\r\n    ('total_open_issues',  issues_total::numeric),\r\n    ('bugs',          measure_bugs)\r\n) AS m(metric, value)\r\nWHERE value IS NOT NULL\r\nORDER BY \"time\", metric;\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Panel Title", "type": "volkovlabs-echarts-panel"}, {"datasource": {"type": "grafana-bigquery-datasource", "uid": "e7f86f19-df05-482f-b7cb-582b520355cf"}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 15}, "id": 5, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "# Current Company Overall Metrics - Management Report\n", "mode": "markdown"}, "pluginVersion": "10.0.2", "type": "text"}, {"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "links": [{"title": "", "url": "/d/${__dashboard.uid}/${__dashboard}?var-jira_project_id=${__data.fields.jira_project_id}&var-sonar_project_id=${__data.fields.sonar_project_id}"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 17}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "WITH windowed AS (\r\n  SELECT *\r\n  FROM vw_sonarqube_projects_with_analysis\r\n  WHERE analysed_at <= $__timeTo()\r\n),\r\nranked AS (\r\n  SELECT\r\n    w.*,\r\n    ROW_NUMBER() OVER (\r\n      PARTITION BY w.sonarqube_project_id\r\n      ORDER BY w.analysed_at DESC\r\n    ) AS rn\r\n  FROM windowed w\r\n)\r\nSELECT \r\n    spl.company_client_name AS \"Client\",\r\n    spl.jira_project_name AS \"JIRA Project\",\r\n    spl.sonar_project_name AS \"SonarQube Project\",\r\n    TO_CHAR(spl.analysed_at, 'YYYY-MM-DD') AS \"Last Analysis\",\r\n    spl.score AS \"Code Quality Score\",\r\n    spl.measure_open_issues AS \"Open Issues\",\r\n    spl.measure_code_smells AS \"Code Smells\",\r\n        spl.measure_bugs AS \"Total Bugs\",\r\n    spl.measure_total_vulnerabilities AS \"Total Vulnerabilities\",\r\n    spl.measure_blocker_violations AS \"Blocker Vulnerabilities\",\r\n    spl.measure_critical_violations AS \"Critical Vulnerabilities\",\r\n    spl.measure_major_violations AS \"Major Vulnerabilities\",\r\n    spl.measure_coverage AS \"Overall Code Coverage (%)\",\r\n    spl.measure_new_coverage  AS \"New Code Coverage (%)\",\r\n    spl.measure_security_rating_grade AS \"Security Rating\",\r\n    spl.measure_security_rating AS \"Security Rating Number\",\r\n    spl.measure_sqale_rating_grade AS \"Maintainability Rating\",\r\n    spl.measure_sqale_rating AS \"Maintainability Rating Number\",\r\n    spl.measure_reliability_rating_grade AS \"Reliability Rating\",\r\n    spl.measure_reliability_rating AS \"Reliability Rating Number\"\r\nFROM ranked spl\r\nWHERE rn = 1\r\nORDER BY analysed_at;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Company Overall SonarQube Projects Metrics", "type": "table"}], "refresh": false, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "AMP - AMP - Smart Suite", "value": "f9c17ccf-6969-4474-b929-81b918a1ebbb"}, "datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "definition": "SELECT \n  cc.name || ' - ' || jp.name AS __text,\n  jp.id AS __value\nFROM jira_projects jp\nINNER JOIN company_clients cc \n  ON cc.id = jp.company_client_id\nORDER BY cc.name;", "hide": 0, "includeAll": false, "label": "Jira Project ID", "multi": false, "name": "jira_project_id", "options": [], "query": "SELECT \n  cc.name || ' - ' || jp.name AS __text,\n  jp.id AS __value\nFROM jira_projects jp\nINNER JOIN company_clients cc \n  ON cc.id = jp.company_client_id\nORDER BY cc.name;", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "5ee59e58-f95c-4221-995d-c10f548ba7d3", "value": "5ee59e58-f95c-4221-995d-c10f548ba7d3"}, "datasource": {"type": "postgres", "uid": "c2308660-828f-402c-91e3-b16bb2ea14fa"}, "definition": "SELECT * FROM sonarqube_projects", "hide": 2, "includeAll": false, "label": "Sonar Project ID", "multi": false, "name": "sonar_project_id", "options": [], "query": "SELECT * FROM sonarqube_projects", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "2025-06-01T06:00:00.000Z", "to": "2025-07-01T05:59:59.000Z"}, "timepicker": {}, "timezone": "", "title": "steerco-metrics", "uid": "b770fa1c-300a-48b7-9324-391bd589a497", "version": 45, "weekStart": ""}
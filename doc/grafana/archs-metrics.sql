-- BA - Documented Technical Risk Ratio (DTRR)

WITH jira_data AS (
  SELECT
    JSON_VALUE(i.assignee, '$.emailAddress')                  AS assignee_email,
    JSON_VALUE(i.reporter, '$.emailAddress')                  AS reporter_email,
    COALESCE(p.name, JSON_VALUE(i.project, '$.name'))         AS jir_project_name,
    p.`key`                                                   AS jira_project_key,
    i.`key`                                                   AS jira_issue_key,
    i.labels                                                  AS labels,              -- JSON array string
    i.created                                                 AS created,
    -- Parse created (string) into TIMESTAMP, handling common Jira formats
    COALESCE(
      SAFE.PARSE_TIMESTAMP('%Y-%m-%dT%H:%M:%E*S%z', i.created),   -- e.g. 2024-06-12T14:33:21.123+0000
      SAFE.PARSE_TIMESTAMP('%Y-%m-%dT%H:%M:%E*S%Ez', i.created),  -- e.g. 2024-06-12T14:33:21.123+00:00
      SAFE_CAST(i.created AS TIMESTAMP)                           -- fallback if already parsable
    ) AS created_ts,
    COALESCE(JSON_VALUE(i.issuetype, '$.name'), i.issuetype)  AS issuetype,
    COALESCE(JSON_VALUE(i.status, '$.name'), i.status)        AS status,
    i.summary                                                 AS summary
  FROM `dw_ds_jira`.`jira-issues` AS i
  LEFT JOIN `dw_ds_jira`.`jira-projects` AS p
    ON p.`key` = COALESCE(JSON_VALUE(i.project, '$.key'), i.project)
),
jira_arch_risk_issues AS (
  SELECT *
  FROM jira_data
  WHERE issuetype = 'Administrative Task'
    AND EXISTS (
      SELECT 1
      FROM UNNEST(IFNULL(JSON_EXTRACT_ARRAY(labels, '$'), [])) AS lbl
      WHERE JSON_VALUE(lbl, '$') = 'risk'
    )
    AND EXISTS (
      SELECT 1
      FROM UNNEST(IFNULL(JSON_EXTRACT_ARRAY(labels, '$'), [])) AS lbl
      WHERE JSON_VALUE(lbl, '$') = 'architecture'
    )
    -- 🔎 Apply Grafana dashboard time range to the parsed timestamp
    AND $__timeFilter(created_ts)
),
grouped AS (
  SELECT
    reporter_email,
    jira_project_key,
    COUNT(*) AS issues_count,
    COUNTIF(LENGTH(COALESCE(summary, '')) > 16) AS documented_issues_count
  FROM jira_arch_risk_issues
  GROUP BY reporter_email, jira_project_key
),
with_totals AS (
  SELECT
    reporter_email,
    jira_project_key,
    issues_count,
    documented_issues_count,
    SUM(issues_count) OVER (PARTITION BY jira_project_key)           AS issues_per_project,
    SUM(issues_count) OVER ()                                        AS total_issues,
    SUM(documented_issues_count) OVER (PARTITION BY reporter_email)  AS documented_issues_per_reporter,
    SUM(issues_count) OVER (PARTITION BY reporter_email)             AS issues_per_reporter
  FROM grouped
)
SELECT
  t.*,
  SAFE_DIVIDE(t.documented_issues_per_reporter, t.issues_per_reporter)                         AS documented_ratio_per_reporter,
  COALESCE(SAFE_DIVIDE(t.documented_issues_per_reporter, t.issues_per_reporter) >= 0.9, FALSE) AS documented_90pct_met
FROM with_totals AS t
ORDER BY t.jira_project_key, t.reporter_email;


-- 
# Task List: Add Multi-Select Functionality to SonarQube Issues Table

## Overview
Add checkbox selection functionality to the SonarQube issues table with persistent selection across pagination and prepare for AI-powered batch issue fixing.

## Phase 1: State Management Setup

### Task 1.1: Add Selection State Variables
- Add `selectedIssues` state as `Set<string>` to store selected issue keys
- Add computed value `selectAllChecked` to determine select-all checkbox state
- Initialize state with empty Set

### Task 1.2: Implement Selection Handler Functions
- Create `handleIssueSelect(issueKey: string)` function to toggle individual issue selection
- Create `handleSelectAll()` function to select/deselect all issues on current page
- Ensure handlers properly update the selectedIssues Set

## Phase 2: UI Components Implementation

### Task 2.1: Add Checkbox Column to Table Header
- Add new table header column with width class `w-12`
- Insert checkbox input with `checkbox checkbox-sm` classes
- Bind checkbox to `selectAllChecked` state and `handleSelectAll` handler
- Position as first column before "Severity"

### Task 2.2: Add Checkbox Column to Table Rows
- Add new table cell as first column in each row
- Insert checkbox input with `checkbox checkbox-sm` classes
- Bind checkbox checked state to `selectedIssues.has(issue.key)`
- Bind onChange to `handleIssueSelect(issue.key)`

### Task 2.3: Add Visual Feedback for Selected Rows
- Update table row className to include conditional highlighting
- Add `bg-primary/10` background class when row is selected
- Maintain existing `hover` class

## Phase 3: Pagination Persistence

### Task 3.1: Update Pagination Handler
- Modify existing `onPageChange` function in Paginator component
- Add `preserveState: true` to router.get options
- Add `preserveScroll: true` to router.get options
- Ensure selectedIssues state persists across page changes

## Phase 4: Bulk Actions Interface

### Task 4.1: Create Selection Summary Bar
- Add conditional rendering block that shows when `selectedIssues.size > 0`
- Use `bg-base-200 p-4 rounded-lg mb-4` styling
- Display count of selected issues with proper pluralization
- Position between breadcrumbs and main table

### Task 4.2: Add Clear Selection Button
- Add "Clear Selection" button with `btn btn-sm btn-ghost` classes
- Bind onClick to reset selectedIssues to empty Set
- Position in left side of summary bar

### Task 4.3: Add Generate AI Prompt Button
- Add primary button with text "Generate AI Fix Prompt ({count})"
- Use `btn btn-primary` classes
- Disable when `selectedIssues.size === 0`
- Position in right side of summary bar

### Task 4.4: Create Placeholder AI Handler Function
- Create `handleGenerateAIPrompt()` function
- Convert selectedIssues Set to Array for API preparation
- Add console.log for debugging selected issue keys
- Add temporary alert showing count of selected issues
- Add TODO comment for future API implementation

## Phase 5: Enhanced User Experience

### Task 5.1: Add Selection Count Display
- Show selected count in format: "X issue(s) selected"
- Handle singular/plural text properly
- Update count reactively as selection changes

### Task 5.2: Implement Selection Validation
- Ensure selection state updates correctly when issues list changes
- Handle edge cases like empty results or page changes
- Maintain selection integrity across data updates

## Phase 6: Optional Enhancements

### Task 6.1: Add Keyboard Shortcuts (Optional)
- Implement Ctrl/Cmd + A for select all visible issues
- Implement Escape key to clear all selections
- Add event listeners and cleanup

### Task 6.2: Add Local Storage Persistence (Optional)
- Store selected issue keys in localStorage
- Restore selection on component mount
- Clear localStorage when navigating away from page

## Implementation Notes

- Use `Set<string>` for efficient O(1) lookup operations
- Store only issue keys (strings) not full issue objects
- Maintain existing TypeScript interfaces without modification
- Follow existing DaisyUI component patterns
- Prepare for future API integration without implementing the actual call
- Ensure all changes maintain existing functionality
- Test selection persistence across pagination thoroughly

## Success Criteria

- [ ] Individual checkboxes work for each issue row
- [ ] Select all checkbox works for current page
- [ ] Selection persists when navigating between pages
- [ ] Selected count displays correctly
- [ ] Clear selection button works
- [ ] Generate AI Prompt button is enabled/disabled correctly
- [ ] Visual feedback shows selected rows
- [ ] No existing functionality is broken

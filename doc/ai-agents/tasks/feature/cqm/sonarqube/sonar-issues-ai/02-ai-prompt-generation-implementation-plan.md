# AI Prompt Generation Implementation Plan

## Overview
Implement a new interactor and endpoint to generate AI prompts for fixing selected SonarQube issues. This feature will integrate with the existing multi-select functionality in the frontend to provide AI-powered batch issue resolution.

## Architecture Overview

### Components to Implement
1. **AI Prompt Interactor** (`libs/intranet/usecase/cqm/interactor/ai_prompt_interactor.go`)
2. **AI Prompt HTTP Endpoint** (`services/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt/`)
3. **Comprehensive Tests** for the interactor
4. **Integration** with existing CQM module

## Implementation Plan

### Phase 1: Core Interactor Development

#### Task 1.1: Create AI Prompt Interactor Structure
- **File**: `libs/intranet/usecase/cqm/interactor/ai_prompt_interactor.go`
- **Dependencies**: 
  - SonarQube client for fetching issue details
  - SonarQube project repository
  - SonarQube issue repository
- **Core Methods**:
  - `NewAIPromptInteractor(i *do.Injector) (*AIPromptInteractor, error)`
  - `GeneratePrompt(ctx context.Context, projectID string, issueKeys []string) (AIPromptResponse, error)`

#### Task 1.2: Define Data Transfer Objects
- **Input DTO**: `GenerateAIPromptRequest`
  ```go
  type GenerateAIPromptRequest struct {
      ProjectID  string   `json:"projectId" validate:"required,uuid"`
      IssueKeys  []string `json:"issueKeys" validate:"required,min=1,dive,required"`
  }
  ```
- **Output DTO**: `AIPromptResponse`
  ```go
  type AIPromptResponse struct {
      Prompt        string            `json:"prompt"`
      IssueCount    int              `json:"issueCount"`
      ProjectInfo   ProjectInfo      `json:"projectInfo"`
      Issues        []IssueDetail    `json:"issues"`
      GeneratedAt   time.Time        `json:"generatedAt"`
  }
  ```

#### Task 1.3: Implement Core Business Logic
- **Validation**: Validate project exists and user has access
- **Issue Fetching**: Retrieve detailed issue information from SonarQube
- **Prompt Generation**: Create structured AI prompt using the template
- **Error Handling**: Comprehensive error handling with proper error types

### Phase 2: HTTP Endpoint Implementation

#### Task 2.1: Create HTTP Controller
- **File**: `services/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt/ai_prompt.go`
- **Endpoint**: `POST /cqm/sonarqube/projects/{projectID}/ai-prompt`
- **Request Handling**:
  - JSON body parsing
  - Input validation
  - Response formatting

#### Task 2.2: Route Registration
- **Integration**: Add route to existing CQM routes
- **Middleware**: Apply standard middleware (auth, validation, etc.)
- **Documentation**: Add endpoint documentation

### Phase 3: Testing Implementation

#### Task 3.1: Unit Tests for Interactor
- **File**: `libs/intranet/usecase/cqm/interactor/ai_prompt_interactor_test.go`
- **Test Coverage**:
  - Happy path scenarios
  - Error scenarios (invalid project, missing issues, etc.)
  - Edge cases (empty issue list, malformed data)
  - Mock SonarQube API responses

#### Task 3.2: Integration Tests
- **Database Integration**: Test with real database using testhelpers
- **SonarQube Mock**: Mock SonarQube API responses
- **End-to-End**: Test complete flow from HTTP request to response

#### Task 3.3: HTTP Endpoint Tests
- **File**: `services/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt/ai_prompt_test.go`
- **Test Coverage**:
  - Valid requests
  - Invalid JSON
  - Validation errors
  - HTTP status codes

### Phase 4: Integration and Registration

#### Task 4.1: Register Interactor in DI Container
- **File**: `libs/intranet/usecase/cqm/cqm.go`
- **Registration**: Add `do.Provide(i, interactor.NewAIPromptInteractor)`

#### Task 4.2: Register HTTP Routes
- **File**: `services/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt/ai_prompt.go`
- **Integration**: Add route registration to main router

## Detailed Implementation Specifications

### AI Prompt Template Structure
```markdown
# SonarQube Issues AI Fix Prompt

## Project Information
- **Project**: {project_name}
- **Branch**: {branch}
- **Total Issues**: {issue_count}

## Issues to Fix

{for each issue}
### Issue {index}: {issue_key}
- **Severity**: {severity}
- **Type**: {type}
- **Message**: {message}
- **Component**: {component}
- **Rule**: {rule}
- **Effort**: {effort}
- **Tags**: {tags}
- **SonarQube URL**: {sonar_url}

{end for}

## Instructions
Please analyze the above SonarQube issues and provide:
1. Root cause analysis for each issue
2. Specific code fixes with examples
3. Best practices to prevent similar issues
4. Priority order for fixing (if multiple issues)

## Guidelines
- Follow the project's existing code style and architecture
- Ensure fixes don't introduce new issues
- Consider performance implications
- Provide clear, actionable solutions
```

### Error Handling Strategy
```go
var (
    ErrProjectNotFound     = errors.New("project not found")
    ErrInvalidIssueKeys    = errors.New("invalid issue keys provided")
    ErrIssuesNotFound      = errors.New("some issues not found in SonarQube")
    ErrPromptGeneration    = errors.New("failed to generate AI prompt")
)
```

### Validation Rules
- `projectID`: Must be valid UUID and exist in database
- `issueKeys`: Must be non-empty array, each key must be valid SonarQube issue key
- **Business Rules**:
  - Maximum 50 issues per request (to prevent prompt size issues)
  - User must have access to the project
  - All issues must belong to the specified project

## Testing Strategy

### Test Data Setup
- Use `testhelpers.Bootstrap` with PostgreSQL
- Create test SonarQube project and Jira project
- Mock SonarQube API responses with realistic data
- Use table-driven tests for comprehensive coverage

### Coverage Requirements
- **Target**: 80% minimum code coverage
- **Focus Areas**:
  - All business logic paths
  - Error scenarios
  - Edge cases
  - Integration points

### Mock Strategy
- Mock SonarQube API using `httptest`
- Mock database operations where appropriate
- Use dependency injection for testability

## Success Criteria
- [ ] AI Prompt Interactor implemented with full business logic
- [ ] HTTP endpoint created and properly integrated
- [ ] Comprehensive test suite with 80%+ coverage
- [ ] All tests pass in CI/CD pipeline
- [ ] Integration with existing CQM module complete
- [ ] Error handling covers all edge cases
- [ ] Input validation prevents malformed requests
- [ ] Generated prompts are well-structured and actionable

## Implementation Task List

### Phase 1: Core Interactor Development
- [ ] **Task 1.1**: Create AI Prompt Interactor Structure
  - File: `libs/intranet/usecase/cqm/interactor/ai_prompt_interactor.go`
  - Dependencies: SonarQube client, repositories, validator
  - Constructor with dependency injection

- [ ] **Task 1.2**: Define Data Transfer Objects
  - Input/Output DTOs with validation tags
  - Error types and constants
  - Helper types for structured data

- [ ] **Task 1.3**: Implement Core Business Logic
  - Project validation and access control
  - Issue fetching from SonarQube API
  - Prompt template generation
  - Comprehensive error handling

### Phase 2: HTTP Endpoint Implementation
- [ ] **Task 2.1**: Create HTTP Controller
  - File: `services/frontend/internal/ui/http/v1/cqm/sonarqube/ai-prompt/ai_prompt.go`
  - POST endpoint with JSON request/response
  - Input validation and error handling

- [ ] **Task 2.2**: Register Routes and Middleware
  - Route registration in main router
  - Apply authentication and validation middleware
  - CORS and security headers

### Phase 3: Testing Implementation
- [ ] **Task 3.1**: Unit Tests for Interactor
  - File: `libs/intranet/usecase/cqm/interactor/ai_prompt_interactor_test.go`
  - Table-driven tests with comprehensive scenarios
  - Mock dependencies and API responses

- [ ] **Task 3.2**: Integration Tests
  - Database integration with testhelpers
  - SonarQube API mocking with httptest
  - End-to-end flow validation

- [ ] **Task 3.3**: HTTP Endpoint Tests
  - HTTP request/response testing
  - Status code validation
  - Error scenario coverage

### Phase 4: Integration and Registration
- [ ] **Task 4.1**: Register Interactor in DI Container
  - Add to `libs/intranet/usecase/cqm/cqm.go`
  - Dependency injection configuration

- [ ] **Task 4.2**: Register HTTP Routes
  - Integrate with existing route structure
  - Update route documentation

## Code Examples

### Interactor Structure
```go
type AIPromptInteractor struct {
    sonarqube        *sonarqube.Client
    sonarProjectRepo repository.SonarqubeProjectRepository
    sonarIssueRepo   repository.SonarqubeIssueRepository
    validator        *validator.Validate
    sonarConfig      sonarqube.ClientConfig
}
```

### HTTP Endpoint
```go
func (c *Controller) GenerateAIPromptHandler() func(next http.Handler) http.Handler {
    return func(w http.ResponseWriter, r *http.Request) {
        var req GenerateAIPromptRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            http.Error(w, "Invalid JSON", http.StatusBadRequest)
            return
        }

        projectID := r.PathValue("projectID")
        req.ProjectID = projectID

        response, err := c.aiPromptUsecase.GeneratePrompt(r.Context(), req)
        if err != nil {
            handleError(w, err)
            return
        }

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(response)
    }
}
```

## Future Enhancements
- **Caching**: Cache generated prompts for repeated requests
- **Templates**: Allow customizable prompt templates
- **AI Integration**: Direct integration with AI services (OpenAI, Claude, etc.)
- **Batch Processing**: Support for very large issue sets
- **Analytics**: Track prompt generation usage and effectiveness
